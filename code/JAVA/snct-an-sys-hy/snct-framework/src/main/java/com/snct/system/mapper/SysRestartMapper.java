package com.snct.system.mapper;

import com.snct.system.domain.SysRestart;

import java.util.List;

/**
 * 数据权限Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-08
 */
public interface SysRestartMapper
{
    /**
     * 查询数据权限
     * 
     * @param restartId 数据权限主键
     * @return 数据权限
     */
    public SysRestart selectSysRestartByRestartId(Long restartId);

    /**
     * 查询数据权限列表
     * 
     * @param sysrestart 数据权限
     * @return 数据权限集合
     */
    public List<SysRestart> selectSysRestartList(SysRestart sysrestart);

    /**
     * 新增数据权限
     * 
     * @param sysrestart 数据权限
     * @return 结果
     */
    public int insertSysRestart(SysRestart sysrestart);

    /**
     * 修改数据权限
     * 
     * @param sysrestart 数据权限
     * @return 结果
     */
    public int updateSysRestart(SysRestart sysrestart);

    /**
     * 删除数据权限
     * 
     * @param restartId 数据权限主键
     * @return 结果
     */
    public int deleteSysRestartByRestartId(Long restartId);

    /**
     * 批量删除数据权限
     * 
     * @param restartIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysRestartByRestartIds(Long[] restartIds);


    /**
     * 校验数据权限名称
     *
     * @param restartName 数据权限名称
     * @return 结果
     */
    public SysRestart checkRestartNameUnique(String restartName);

    /**
     * 查询所有活跃的重启任务
     *
     * @return 活跃的重启任务列表
     */
    public List<SysRestart> selectActiveRestartTasks();

}
