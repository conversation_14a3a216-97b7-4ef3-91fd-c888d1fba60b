package com.snct.system.service;

import com.snct.system.domain.SysRestart;

import java.util.List;

/**
 * 数据权限Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-08
 */
public interface ISysSysRestartService
{
    /**
     * 查询数据权限
     * 
     * @param restartId 数据权限主键
     * @return 数据权限
     */
    public SysRestart selectSysRestartByRestartId(Long restartId);

    /**
     * 查询数据权限列表
     * 
     * @param sysrestart 数据权限
     * @return 数据权限集合
     */
    public List<SysRestart> selectSysRestartList(SysRestart sysrestart);

    /**
     * 新增数据权限
     * 
     * @param sysrestart 数据权限
     * @return 结果
     */
    public int insertSysRestart(SysRestart sysrestart);

    /**
     * 修改数据权限
     * 
     * @param sysrestart 数据权限
     * @return 结果
     */
    public int updateSysRestart(SysRestart sysrestart);

    /**
     * 批量删除数据权限
     * 
     * @param restartIds 需要删除的数据权限主键集合
     * @return 结果
     */
    public int deleteSysRestartByRestartIds(Long[] restartIds);

    /**
     * 删除数据权限信息
     * 
     * @param restartId 数据权限主键
     * @return 结果
     */
    public int deleteSysRestartByRestartId(Long restartId);


    /**
     * 校验数据权限名称
     *
     * @param sysrestart 权限信息
     * @return 结果
     */
    public boolean checkRestartNameUnique(SysRestart sysrestart);

    /**
     * 检查并执行定时重启任务
     * 读取定时重启表数据，判断当前时间是否需要执行重启
     */
    public void checkAndExecuteRestartTasks();


}
