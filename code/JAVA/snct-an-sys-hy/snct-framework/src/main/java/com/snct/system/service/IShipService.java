package com.snct.system.service;

import com.snct.system.domain.Ship;

import java.util.List;

/**
 * 船只Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-08
 */
public interface IShipService 
{
    /**
     * 查询船只
     * 
     * @param shipId 船只主键
     * @return 船只
     */
    public Ship selectShipByShipId(Long shipId);

    /**
     * 查询船只列表
     * 
     * @param ship 船只
     * @return 船只集合
     */
    public List<Ship> selectShipList(Ship ship);

    /**
     * 新增船只
     * 
     * @param ship 船只
     * @return 结果
     */
    public int insertShip(Ship ship);

    /**
     * 修改船只
     * 
     * @param ship 船只
     * @return 结果
     */
    public int updateShip(Ship ship);

    /**
     * 批量删除船只
     * 
     * @param shipIds 需要删除的船只主键集合
     * @return 结果
     */
    public int deleteShipByShipIds(Long[] shipIds);

    /**
     * 删除船只信息
     * 
     * @param shipId 船只主键
     * @return 结果
     */
    public int deleteShipByShipId(Long shipId);
}
