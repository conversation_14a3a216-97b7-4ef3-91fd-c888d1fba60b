package com.snct.system.domain.msg;

import com.snct.common.annotation.Excel;
import com.snct.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;


/**
 * 功放消息对象 bu_msg_amplifier
 * 
 * <AUTHOR>
 * @date 2025-04-24
 */
public class BuMsgAmplifier extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 部门ID */
    @Excel(name = "部门ID")
    private Long deptId;

    /** 部门名称 */
    @Excel(name = "部门名称")
    private String deptName;

    /** 船舶名称 */
    @Excel(name = "船舶名称")
    private String shipName;

    /** 船只id */
    @Excel(name = "船只id")
    private Long shipId;

    /** 设备id */
    @Excel(name = "设备id")
    private Long deviceId;

    /** 衰减值 */
    @Excel(name = "衰减值")
    private Double decay;

    /** 温度 */
    @Excel(name = "温度")
    private Double temp;

    /** 输出功率 */
    @Excel(name = "输出功率")
    private Double outPower;

    /** 设备状态 */
    @Excel(name = "设备状态")
    private Long bucStatus;

    /** 状态 0默认 1发送云端成功 2发送云端失败 */
    @Excel(name = "状态 0默认 1发送云端成功 2发送云端失败")
    private Long status;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setDeptId(Long deptId) 
    {
        this.deptId = deptId;
    }

    public Long getDeptId() 
    {
        return deptId;
    }

    public String getDeptName()
    {
        return deptName;
    }

    public void setDeptName(String deptName)
    {
        this.deptName = deptName;
    }

    public void setShipId(Long shipId) 
    {
        this.shipId = shipId;
    }

    public Long getShipId() 
    {
        return shipId;
    }

    public String getShipName()
    {
        return shipName;
    }

    public void setShipName(String shipName)
    {
        this.shipName = shipName;
    }

    public void setDeviceId(Long deviceId)
    {
        this.deviceId = deviceId;
    }

    public Long getDeviceId()
    {
        return deviceId;
    }

    public void setDecay(Double decay) 
    {
        this.decay = decay;
    }

    public Double getDecay() 
    {
        return decay;
    }

    public void setTemp(Double temp) 
    {
        this.temp = temp;
    }

    public Double getTemp() 
    {
        return temp;
    }

    public void setOutPower(Double outPower) 
    {
        this.outPower = outPower;
    }

    public Double getOutPower() 
    {
        return outPower;
    }

    public void setBucStatus(Long bucStatus) 
    {
        this.bucStatus = bucStatus;
    }

    public Long getBucStatus() 
    {
        return bucStatus;
    }

    public void setStatus(Long status) 
    {
        this.status = status;
    }

    public Long getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("deptId", getDeptId())
            .append("deptName", getDeptName())
            .append("shipId", getShipId())
            .append("shipName", getShipName())
            .append("deviceId", getDeviceId())
            .append("decay", getDecay())
            .append("temp", getTemp())
            .append("outPower", getOutPower())
            .append("bucStatus", getBucStatus())
            .append("status", getStatus())
            .append("createTime", getCreateTime())
            .toString();
    }
}
