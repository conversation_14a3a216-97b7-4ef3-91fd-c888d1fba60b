package com.snct.system.mapper;

import com.snct.system.domain.DataStatistics;

import java.util.List;

/**
 * 采集-传输记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-09
 */
public interface DataStatisticsMapper 
{
    /**
     * 查询采集-传输记录
     * 
     * @param id 采集-传输记录主键
     * @return 采集-传输记录
     */
    public DataStatistics selectDataStatisticsById(Long id);

    /**
     * 查询采集-传输记录列表
     * 
     * @param dataStatistics 采集-传输记录
     * @return 采集-传输记录集合
     */
    public List<DataStatistics> selectDataStatisticsList(DataStatistics dataStatistics);

    /**
     * 新增采集-传输记录
     * 
     * @param dataStatistics 采集-传输记录
     * @return 结果
     */
    public int insertDataStatistics(DataStatistics dataStatistics);

    /**
     * 修改采集-传输记录
     * 
     * @param dataStatistics 采集-传输记录
     * @return 结果
     */
    public int updateDataStatistics(DataStatistics dataStatistics);

    /**
     * 删除采集-传输记录
     * 
     * @param id 采集-传输记录主键
     * @return 结果
     */
    public int deleteDataStatisticsById(Long id);

    /**
     * 批量删除采集-传输记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDataStatisticsByIds(Long[] ids);
}
