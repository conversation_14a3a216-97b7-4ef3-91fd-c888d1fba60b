package com.snct.system.service;

import com.snct.system.domain.msg.BuMsgPdu;

import java.util.List;

/**
 * pdu消息Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-24
 */
public interface IBuMsgPduService 
{
    /**
     * 查询pdu消息
     * 
     * @param id pdu消息主键
     * @return pdu消息
     */
    public BuMsgPdu selectBuMsgPduById(Long id);

    /**
     * 查询pdu消息列表
     * 
     * @param buMsgPdu pdu消息
     * @return pdu消息集合
     */
    public List<BuMsgPdu> selectBuMsgPduList(BuMsgPdu buMsgPdu);

    /**
     * 新增pdu消息
     * 
     * @param buMsgPdu pdu消息
     * @return 结果
     */
    public int insertBuMsgPdu(BuMsgPdu buMsgPdu);

    /**
     * 修改pdu消息
     * 
     * @param buMsgPdu pdu消息
     * @return 结果
     */
    public int updateBuMsgPdu(BuMsgPdu buMsgPdu);

    /**
     * 批量删除pdu消息
     * 
     * @param ids 需要删除的pdu消息主键集合
     * @return 结果
     */
    public int deleteBuMsgPduByIds(Long[] ids);

    /**
     * 删除pdu消息信息
     * 
     * @param id pdu消息主键
     * @return 结果
     */
    public int deleteBuMsgPduById(Long id);
}
