package com.snct.system.service.impl;

import com.snct.common.utils.DateUtils;
import com.snct.system.domain.SerialConfig;
import com.snct.system.mapper.SerialConfigMapper;
import com.snct.system.service.ISerialConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 串口配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-09
 */
@Service
public class SerialConfigServiceImpl implements ISerialConfigService 
{
    @Autowired
    private SerialConfigMapper serialConfigMapper;

    /**
     * 查询串口配置
     * 
     * @param id 串口配置主键
     * @return 串口配置
     */
    @Override
    public SerialConfig selectSerialConfigById(Long id)
    {
        return serialConfigMapper.selectSerialConfigById(id);
    }

    /**
     * 查询串口配置列表
     * 
     * @param serialConfig 串口配置
     * @return 串口配置
     */
    @Override
    public List<SerialConfig> selectSerialConfigList(SerialConfig serialConfig)
    {
        return serialConfigMapper.selectSerialConfigList(serialConfig);
    }

    /**
     * 新增串口配置
     * 
     * @param serialConfig 串口配置
     * @return 结果
     */
    @Override
    public int insertSerialConfig(SerialConfig serialConfig)
    {
        serialConfig.setCreateTime(DateUtils.getNowDate());
        return serialConfigMapper.insertSerialConfig(serialConfig);
    }

    /**
     * 修改串口配置
     * 
     * @param serialConfig 串口配置
     * @return 结果
     */
    @Override
    public int updateSerialConfig(SerialConfig serialConfig)
    {
        serialConfig.setUpdateTime(DateUtils.getNowDate());
        return serialConfigMapper.updateSerialConfig(serialConfig);
    }

    /**
     * 批量删除串口配置
     * 
     * @param ids 需要删除的串口配置主键
     * @return 结果
     */
    @Override
    public int deleteSerialConfigByIds(Long[] ids)
    {
        return serialConfigMapper.deleteSerialConfigByIds(ids);
    }

    /**
     * 删除串口配置信息
     * 
     * @param id 串口配置主键
     * @return 结果
     */
    @Override
    public int deleteSerialConfigById(Long id)
    {
        return serialConfigMapper.deleteSerialConfigById(id);
    }

    /**
     * 查询单个串口名称配置
     * @param serialConfig
     * @return
     */
    public SerialConfig selectSerialConfig(SerialConfig serialConfig){
        return serialConfigMapper.selectSerialConfig(serialConfig);
    }
}
