package com.snct.system.domain;

import com.snct.common.annotation.Excel;
import com.snct.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 数据权限对象 sys_data_auth
 * 
 * <AUTHOR>
 * @date 2025-04-08
 */
public class SysDataAuth extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 数据权限ID */
    private Long dataAuthId;

    /** 数据权限编码 */
    @Excel(name = "数据权限编码")
    private String dataAuthCode;

    /** 数据权限名称 */
    @Excel(name = "数据权限名称")
    private String dataAuthName;

    /** 显示顺序 */
    @Excel(name = "显示顺序")
    private Integer dataAuthSort;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    public void setDataAuthId(Long dataAuthId) 
    {
        this.dataAuthId = dataAuthId;
    }

    public Long getDataAuthId() 
    {
        return dataAuthId;
    }

    public void setDataAuthCode(String dataAuthCode) 
    {
        this.dataAuthCode = dataAuthCode;
    }

    public String getDataAuthCode() 
    {
        return dataAuthCode;
    }

    public void setDataAuthName(String dataAuthName) 
    {
        this.dataAuthName = dataAuthName;
    }

    public String getDataAuthName() 
    {
        return dataAuthName;
    }

    public void setDataAuthSort(Integer dataAuthSort) 
    {
        this.dataAuthSort = dataAuthSort;
    }

    public Integer getDataAuthSort() 
    {
        return dataAuthSort;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("dataAuthId", getDataAuthId())
            .append("dataAuthCode", getDataAuthCode())
            .append("dataAuthName", getDataAuthName())
            .append("dataAuthSort", getDataAuthSort())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
