package com.snct.system.service.impl;

import com.snct.common.constant.UserConstants;
import com.snct.common.utils.DateUtils;
import com.snct.common.utils.StringUtils;
import com.snct.system.domain.SysDataAuth;
import com.snct.system.mapper.SysDataAuthMapper;
import com.snct.system.service.ISysDataAuthService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 数据权限Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-08
 */
@Service
public class SysDataAuthServiceImpl implements ISysDataAuthService 
{
    @Autowired
    private SysDataAuthMapper sysDataAuthMapper;

    /**
     * 查询数据权限
     * 
     * @param dataAuthId 数据权限主键
     * @return 数据权限
     */
    @Override
    public SysDataAuth selectSysDataAuthByDataAuthId(Long dataAuthId)
    {
        return sysDataAuthMapper.selectSysDataAuthByDataAuthId(dataAuthId);
    }

    /**
     * 查询数据权限列表
     * 
     * @param sysDataAuth 数据权限
     * @return 数据权限
     */
    @Override
    public List<SysDataAuth> selectSysDataAuthList(SysDataAuth sysDataAuth)
    {
        return sysDataAuthMapper.selectSysDataAuthList(sysDataAuth);
    }

    /**
     * 新增数据权限
     * 
     * @param sysDataAuth 数据权限
     * @return 结果
     */
    @Override
    public int insertSysDataAuth(SysDataAuth sysDataAuth)
    {
        sysDataAuth.setCreateTime(DateUtils.getNowDate());
        return sysDataAuthMapper.insertSysDataAuth(sysDataAuth);
    }

    /**
     * 修改数据权限
     * 
     * @param sysDataAuth 数据权限
     * @return 结果
     */
    @Override
    public int updateSysDataAuth(SysDataAuth sysDataAuth)
    {
        sysDataAuth.setUpdateTime(DateUtils.getNowDate());
        return sysDataAuthMapper.updateSysDataAuth(sysDataAuth);
    }

    /**
     * 批量删除数据权限
     * 
     * @param dataAuthIds 需要删除的数据权限主键
     * @return 结果
     */
    @Override
    public int deleteSysDataAuthByDataAuthIds(Long[] dataAuthIds)
    {
        return sysDataAuthMapper.deleteSysDataAuthByDataAuthIds(dataAuthIds);
    }

    /**
     * 删除数据权限信息
     * 
     * @param dataAuthId 数据权限主键
     * @return 结果
     */
    @Override
    public int deleteSysDataAuthByDataAuthId(Long dataAuthId)
    {
        return sysDataAuthMapper.deleteSysDataAuthByDataAuthId(dataAuthId);
    }


    /**
     * 校验数据权限名称是否唯一
     *
     * @param sysDataAuth 数据权限信息
     * @return 结果
     */
    @Override
    public boolean checkDataAuthNameUnique(SysDataAuth sysDataAuth)
    {
        Long dataAuthId = StringUtils.isNull(sysDataAuth.getDataAuthId()) ? -1L : sysDataAuth.getDataAuthId();
        SysDataAuth info = sysDataAuthMapper.checkDataAuthNameUnique(sysDataAuth.getDataAuthName());
        if (StringUtils.isNotNull(info) && info.getDataAuthId().longValue() != dataAuthId.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

}
