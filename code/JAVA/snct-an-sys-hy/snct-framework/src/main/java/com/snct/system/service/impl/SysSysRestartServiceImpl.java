package com.snct.system.service.impl;

import com.snct.common.constant.UserConstants;
import com.snct.common.utils.DateUtils;
import com.snct.common.utils.StringUtils;
import com.snct.system.domain.SysRestart;
import com.snct.system.mapper.SysRestartMapper;
import com.snct.system.service.ISysSysRestartService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;

/**
 * 数据权限Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-08
 */
@Service
public class SysSysRestartServiceImpl implements ISysSysRestartService
{
    private static final Logger logger = LoggerFactory.getLogger(SysSysRestartServiceImpl.class);

    @Autowired
    private SysRestartMapper sysRestartMapper;

    /**
     * 查询数据权限
     * 
     * @param restartId 数据权限主键
     * @return 数据权限
     */
    @Override
    public SysRestart selectSysRestartByRestartId(Long restartId)
    {
        return sysRestartMapper.selectSysRestartByRestartId(restartId);
    }

    /**
     * 查询数据权限列表
     * 
     * @param sysrestart 数据权限
     * @return 数据权限
     */
    @Override
    public List<SysRestart> selectSysRestartList(SysRestart sysrestart)
    {
        return sysRestartMapper.selectSysRestartList(sysrestart);
    }

    /**
     * 新增数据权限
     * 
     * @param sysrestart 数据权限
     * @return 结果
     */
    @Override
    public int insertSysRestart(SysRestart sysrestart)
    {
        sysrestart.setCreateTime(DateUtils.getNowDate());
        return sysRestartMapper.insertSysRestart(sysrestart);
    }

    /**
     * 修改数据权限
     * 
     * @param sysrestart 数据权限
     * @return 结果
     */
    @Override
    public int updateSysRestart(SysRestart sysrestart)
    {
        sysrestart.setUpdateTime(DateUtils.getNowDate());
        return sysRestartMapper.updateSysRestart(sysrestart);
    }

    /**
     * 批量删除数据权限
     * 
     * @param restartIds 需要删除的数据权限主键
     * @return 结果
     */
    @Override
    public int deleteSysRestartByRestartIds(Long[] restartIds)
    {
        return sysRestartMapper.deleteSysRestartByRestartIds(restartIds);
    }

    /**
     * 删除数据权限信息
     * 
     * @param restartId 数据权限主键
     * @return 结果
     */
    @Override
    public int deleteSysRestartByRestartId(Long restartId)
    {
        return sysRestartMapper.deleteSysRestartByRestartId(restartId);
    }


    /**
     * 校验数据权限名称是否唯一
     *
     * @param sysrestart 数据权限信息
     * @return 结果
     */
    @Override
    public boolean checkRestartNameUnique(SysRestart sysrestart)
    {
        Long restartId = StringUtils.isNull(sysrestart.getRestartId()) ? -1L : sysrestart.getRestartId();
        SysRestart info = sysRestartMapper.checkRestartNameUnique(sysrestart.getRestartName());
        if (StringUtils.isNotNull(info) && info.getRestartId().longValue() != restartId.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 检查并执行定时重启任务
     * 读取定时重启表数据，判断当前时间是否需要执行重启
     */
    @Override
    public void checkAndExecuteRestartTasks()
    {
        try {
            // 获取所有活跃的重启任务
            List<SysRestart> activeRestartTasks = sysRestartMapper.selectActiveRestartTasks();
            if (activeRestartTasks == null || activeRestartTasks.isEmpty()) {
                //logger.debug("没有找到活跃的重启任务");
                return;
            }

            // 获取当前时间信息
            LocalDateTime now = LocalDateTime.now();
            String currentTime = now.format(DateTimeFormatter.ofPattern("HH:mm:ss"));
            int currentDayOfWeek = now.getDayOfWeek().getValue(); // 1=Monday, 7=Sunday

            logger.info("系统任务：当前时间: {}, 星期: {}", currentTime, currentDayOfWeek);

            for (SysRestart restartTask : activeRestartTasks) {
                if (shouldExecuteRestart(restartTask, currentTime, currentDayOfWeek)) {
                    logger.info("执行重启任务: {}", restartTask.getRestartName());
                    restartTask.setDoTime(new Date());
                    sysRestartMapper.updateSysRestart(restartTask);
                    executeSystemReboot();
                    return; // 执行重启后直接返回，因为系统将重启
                }
            }

            //logger.debug("当前时间不匹配任何重启任务");

        } catch (Exception e) {
            logger.error("检查定时重启任务时发生异常", e);
        }
    }

    /**
     * 判断是否应该执行重启任务
     *
     * @param restartTask 重启任务
     * @param currentTime 当前时间 (HH:mm:ss)
     * @param currentDayOfWeek 当前星期几 (1-7)
     * @return 是否应该执行重启
     */
    private boolean shouldExecuteRestart(SysRestart restartTask, String currentTime, int currentDayOfWeek)
    {
        try {
            String restartTime = restartTask.getRestartTime();
            if (StringUtils.isEmpty(restartTime)) {
                logger.warn("重启任务 {} 的重启时间为空", restartTask.getRestartName());
                return false;
            }
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String date = DateUtils.parseTimeToDate(System.currentTimeMillis(),"yyyy-MM-dd").toString();
            // 检查时间是否匹配（精确到分钟）
            String currentTimeMinute = date +" "+ currentTime.substring(0, 5)+":00";
            String restartTimeMinute = date +" "+ restartTime.substring(0, 5)+":00";

            int currentTimeMinute_int = (int)(sdf.parse(currentTimeMinute).getTime()/1000) ;
            int restartTimeMinute_int = (int)(sdf.parse(restartTimeMinute).getTime()/1000) ;

            //如果任务执行时间 与 设定时间相差超过1分钟  或者 重启任务执行时间少于1小时  不执行重启任务
            if (  currentTimeMinute_int < (restartTimeMinute_int-60) ||  currentTimeMinute_int - (restartTimeMinute_int) > 900    ||  currentTimeMinute_int - ((int)(restartTask.getDoTime().getTime()/1000)) < 7200  ) {
                return false;
            }

            Integer restartType = restartTask.getRestartType();

            if (restartType == 1) {
                // 每天重启
                logger.debug("每天重启任务 {} 时间匹配", restartTask.getRestartName());
                return true;
            } else if (restartType == 2) {
                // 每周重启，检查星期几
                String todayStr = restartTask.getToday();
                if (StringUtils.isEmpty(todayStr)) {
                    logger.warn("每周重启任务 {} 的星期设置为空", restartTask.getRestartName());
                    return false;
                }

                // 解析星期几设置，支持多个星期几用逗号分隔
                String[] days = todayStr.split(",");
                for (String day : days) {
                    try {
                        int targetDay = Integer.parseInt(day.trim());
                        if (targetDay == currentDayOfWeek) {
                            logger.debug("每周重启任务 {} 时间和星期都匹配", restartTask.getRestartName());
                            return true;
                        }
                    } catch (NumberFormatException e) {
                        logger.warn("重启任务 {} 的星期设置格式错误: {}", restartTask.getRestartName(), day);
                    }
                }
            } else {
                logger.warn("未知的重启类型: {}", restartType);
            }

        } catch (Exception e) {
            logger.error("判断重启任务 {} 时发生异常", restartTask.getRestartName(), e);
        }

        return false;
    }

    /**
     * 执行系统重启命令
     */
    private void executeSystemReboot()
    {
        try {
            logger.info("######执行系统重启命令######");
            Runtime.getRuntime().exec("reboot");
            logger.info("重启命令已执行");
        } catch (Exception e) {
            logger.error("执行系统重启命令时发生异常", e);
        }
    }

}
