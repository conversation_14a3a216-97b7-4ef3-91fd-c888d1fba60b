package com.snct.system.mapper;

import com.snct.system.domain.msg.BuMsgAmplifier;

import java.util.List;

/**
 * 功放消息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-24
 */
public interface BuMsgAmplifierMapper 
{
    /**
     * 查询功放消息
     * 
     * @param id 功放消息主键
     * @return 功放消息
     */
    public BuMsgAmplifier selectBuMsgAmplifierById(Long id);

    /**
     * 查询功放消息列表
     * 
     * @param buMsgAmplifier 功放消息
     * @return 功放消息集合
     */
    public List<BuMsgAmplifier> selectBuMsgAmplifierList(BuMsgAmplifier buMsgAmplifier);

    /**
     * 新增功放消息
     * 
     * @param buMsgAmplifier 功放消息
     * @return 结果
     */
    public int insertBuMsgAmplifier(BuMsgAmplifier buMsgAmplifier);

    /**
     * 修改功放消息
     * 
     * @param buMsgAmplifier 功放消息
     * @return 结果
     */
    public int updateBuMsgAmplifier(BuMsgAmplifier buMsgAmplifier);

    /**
     * 删除功放消息
     * 
     * @param id 功放消息主键
     * @return 结果
     */
    public int deleteBuMsgAmplifierById(Long id);

    /**
     * 批量删除功放消息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBuMsgAmplifierByIds(Long[] ids);
}
