package com.snct.system.service;

import com.snct.system.domain.SerialConfig;

import java.util.List;

/**
 * 串口配置Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-09
 */
public interface ISerialConfigService 
{
    /**
     * 查询串口配置
     * 
     * @param id 串口配置主键
     * @return 串口配置
     */
    public SerialConfig selectSerialConfigById(Long id);

    /**
     * 查询串口配置列表
     * 
     * @param serialConfig 串口配置
     * @return 串口配置集合
     */
    public List<SerialConfig> selectSerialConfigList(SerialConfig serialConfig);

    /**
     * 新增串口配置
     * 
     * @param serialConfig 串口配置
     * @return 结果
     */
    public int insertSerialConfig(SerialConfig serialConfig);

    /**
     * 修改串口配置
     * 
     * @param serialConfig 串口配置
     * @return 结果
     */
    public int updateSerialConfig(SerialConfig serialConfig);

    /**
     * 批量删除串口配置
     * 
     * @param ids 需要删除的串口配置主键集合
     * @return 结果
     */
    public int deleteSerialConfigByIds(Long[] ids);

    /**
     * 删除串口配置信息
     * 
     * @param id 串口配置主键
     * @return 结果
     */
    public int deleteSerialConfigById(Long id);

    /**
     * 查询单个串口名称配置
     * @param serialConfig
     * @return
     */
    public SerialConfig selectSerialConfig(SerialConfig serialConfig);
}
