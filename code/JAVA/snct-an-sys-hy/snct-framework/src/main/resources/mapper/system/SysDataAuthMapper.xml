<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snct.system.mapper.SysDataAuthMapper">

	<resultMap type="SysDataAuth" id="SysDataAuthResult">
		<id     property="dataAuthId"        column="data_auth_id"       />
		<result property="dataAuthCode"      column="data_auth_code"     />
		<result property="dataAuthName"      column="data_auth_name"     />
		<result property="dataAuthSort"      column="data_auth_sort"     />
		<result property="status"        column="status"        />
		<result property="createBy"      column="create_by"     />
		<result property="createTime"    column="create_time"   />
		<result property="updateBy"      column="update_by"     />
		<result property="updateTime"    column="update_time"   />
		<result property="remark"        column="remark"        />
	</resultMap>
	
	<sql id="selectdataAuthVo">
        select data_auth_id, data_auth_code, data_auth_name, data_auth_sort, status, create_by, create_time, remark
		from sys_data_auth
    </sql>
	
	<select id="selectSysDataAuthList" parameterType="SysDataAuth" resultMap="SysDataAuthResult">
	    <include refid="selectdataAuthVo"/>
		<where>
			<if test="dataAuthCode != null and dataAuthCode != ''">
				AND data_auth_code like concat('%', #{dataAuthCode}, '%')
			</if>
			<if test="status != null and status != ''">
				AND status = #{status}
			</if>
			<if test="dataAuthName != null and dataAuthName != ''">
				AND data_auth_name like concat('%', #{dataAuthName}, '%')
			</if>
		</where>
	</select>
	
	<select id="selectDataAuthAll" resultMap="SysDataAuthResult">
		<include refid="selectdataAuthVo"/>
	</select>

	<select id="selectSysDataAuthByDataAuthId" parameterType="Long" resultMap="SysDataAuthResult">
		<include refid="selectdataAuthVo"/>
		where data_auth_id = #{dataAuthId}
	</select>
	
	<select id="checkDataAuthNameUnique" parameterType="String" resultMap="SysDataAuthResult">
		<include refid="selectdataAuthVo"/>
		 where data_auth_name=#{dataAuthName} limit 1
	</select>
	<select id="checkDataAuthCodeUnique" parameterType="String" resultMap="SysDataAuthResult">
		<include refid="selectdataAuthVo"/>
		 where data_auth_code=#{dataAuthCode} limit 1
	</select>
	
	<update id="updateSysDataAuth" parameterType="sysDataAuth">
 		update sys_data_auth
 		<set>
 			<if test="dataAuthCode != null and dataAuthCode != ''">data_auth_code = #{dataAuthCode},</if>
 			<if test="dataAuthName != null and dataAuthName != ''">data_auth_name = #{dataAuthName},</if>
 			<if test="dataAuthSort != null">data_auth_sort = #{dataAuthSort},</if>
 			<if test="status != null and status != ''">status = #{status},</if>
 			<if test="remark != null">remark = #{remark},</if>
 			<if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
 			update_time = sysdate()
 		</set>
 		where data_auth_id = #{dataAuthId}
	</update>
 	
 	<insert id="insertSysDataAuth" parameterType="SysPost" useGeneratedKeys="true" keyProperty="dataAuthId">
 		insert into sys_data_auth(
 			<if test="dataAuthId != null and dataAuthId != 0">data_auth_id,</if>
 			<if test="dataAuthCode != null and dataAuthCode != ''">data_auth_code,</if>
 			<if test="dataAuthName != null and dataAuthName != ''">data_auth_name,</if>
 			<if test="dataAuthSort != null">data_auth_sort,</if>
 			<if test="status != null and status != ''">status,</if>
 			<if test="remark != null and remark != ''">remark,</if>
 			<if test="createBy != null and createBy != ''">create_by,</if>
 			create_time
 		)values(
 			<if test="dataAuthId != null and dataAuthId != 0">#{dataAuthId},</if>
 			<if test="dataAuthCode != null and dataAuthCode != ''">#{dataAuthCode},</if>
 			<if test="dataAuthName != null and dataAuthName != ''">#{dataAuthName},</if>
 			<if test="dataAuthSort != null">#{dataAuthSort},</if>
 			<if test="status != null and status != ''">#{status},</if>
 			<if test="remark != null and remark != ''">#{remark},</if>
 			<if test="createBy != null and createBy != ''">#{createBy},</if>
 			sysdate()
 		)
	</insert>
	
	<delete id="deleteSysDataAuthByDataAuthId" parameterType="Long">
		delete from sys_data_auth where data_auth_id = #{dataAuthId}
	</delete>
	
	<delete id="deleteSysDataAuthByDataAuthIds" parameterType="Long">
 		delete from sys_data_auth where data_auth_id in
 		<foreach collection="array" item="dataAuthIds" open="(" separator="," close=")">
 			#{dataAuthIds}
        </foreach> 
 	</delete>

</mapper> 