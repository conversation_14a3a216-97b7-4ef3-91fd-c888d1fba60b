<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snct.system.mapper.BuMsgPduOutMapper">
    
    <resultMap type="BuMsgPduOut" id="BuMsgPduOutResult">
        <result property="id"    column="id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="deptName"  column="dept_name"  />
        <result property="deviceId"    column="device_id"    />
        <result property="batchCode"    column="batch_code"    />
        <result property="outIndex"    column="out_index"    />
        <result property="electric"    column="electric"    />
        <result property="power"    column="power"    />
        <result property="outStatus"    column="out_status"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectBuMsgPduOutVo">
        select p.id, p.dept_id, d.dept_name, p.device_id, p.batch_code, p.out_index, p.electric, p.power, p.out_status, p.status, p.create_time
        from bu_msg_pdu_out p
        left join sys_dept d on p.dept_id = d.dept_id
    </sql>

    <select id="selectBuMsgPduOutList" parameterType="BuMsgPduOut" resultMap="BuMsgPduOutResult">
        <include refid="selectBuMsgPduOutVo"/>
        <where>  
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="deviceId != null "> and device_id = #{deviceId}</if>
            <if test="batchCode != null  and batchCode != ''"> and batch_code = #{batchCode}</if>
            <if test="outIndex != null "> and out_index = #{outIndex}</if>
            <if test="electric != null "> and electric = #{electric}</if>
            <if test="power != null "> and power = #{power}</if>
            <if test="outStatus != null "> and out_status = #{outStatus}</if>
            <if test="status != null "> and status = #{status}</if>
            ${params.dataScope}
        </where>
    </select>
    
    <select id="selectBuMsgPduOutById" parameterType="Long" resultMap="BuMsgPduOutResult">
        <include refid="selectBuMsgPduOutVo"/>
        where id = #{id}
    </select>

    <insert id="insertBuMsgPduOut" parameterType="BuMsgPduOut" useGeneratedKeys="true" keyProperty="id">
        insert into bu_msg_pdu_out
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null ">dept_id,</if>
            <if test="deviceId != null ">device_id,</if>
            <if test="batchCode != null and batchCode != ''">batch_code,</if>
            <if test="outIndex != null">out_index,</if>
            <if test="electric != null">electric,</if>
            <if test="power != null">power,</if>
            <if test="outStatus != null">out_status,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null ">#{deptId},</if>
            <if test="deviceId != null ">#{deviceId},</if>
            <if test="batchCode != null and batchCode != ''">#{batchCode},</if>
            <if test="outIndex != null">#{outIndex},</if>
            <if test="electric != null">#{electric},</if>
            <if test="power != null">#{power},</if>
            <if test="outStatus != null">#{outStatus},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateBuMsgPduOut" parameterType="BuMsgPduOut">
        update bu_msg_pdu_out
        <trim prefix="SET" suffixOverrides=",">
            <if test="deptId != null ">dept_id = #{deptId},</if>
            <if test="deviceId != null ">device_id = #{deviceId},</if>
            <if test="batchCode != null and batchCode != ''">batch_code = #{batchCode},</if>
            <if test="outIndex != null">out_index = #{outIndex},</if>
            <if test="electric != null">electric = #{electric},</if>
            <if test="power != null">power = #{power},</if>
            <if test="outStatus != null">out_status = #{outStatus},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBuMsgPduOutById" parameterType="Long">
        delete from bu_msg_pdu_out where id = #{id}
    </delete>

    <delete id="deleteBuMsgPduOutByIds" parameterType="String">
        delete from bu_msg_pdu_out where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>