<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
		PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snct.system.mapper.SerialConfigMapper">

	<resultMap type="SerialConfig" id="SerialConfigResult">
		<result property="id"    column="id"    />
		<result property="oldName"    column="old_name"    />
		<result property="newName"    column="new_name"    />
		<result property="createBy"    column="create_by"    />
		<result property="createTime"    column="create_time"    />
		<result property="updateBy"    column="update_by"    />
		<result property="updateTime"    column="update_time"    />
		<result property="enable"    column="enable"    />
		<result property="remark"    column="remark"    />
	</resultMap>

	<sql id="selectSerialConfigVo">
		select id, old_name, new_name, create_by, create_time, update_by, update_time, enable, remark from bu_serial_config
	</sql>

	<select id="selectSerialConfigList" parameterType="SerialConfig" resultMap="SerialConfigResult">
		<include refid="selectSerialConfigVo"/>
		<where>
			<if test="oldName != null  and oldName != ''"> and old_name like concat('%', #{oldName}, '%')</if>
			<if test="newName != null  and newName != ''"> and new_name like concat('%', #{newName}, '%')</if>
			<if test="enable != null "> and enable = #{enable}</if>
		</where>
	</select>

	<select id="selectSerialConfigById" parameterType="Long" resultMap="SerialConfigResult">
		<include refid="selectSerialConfigVo"/>
		where id = #{id}
	</select>

	<insert id="insertSerialConfig" parameterType="SerialConfig" useGeneratedKeys="true" keyProperty="id">
		insert into bu_serial_config
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="oldName != null and oldName != ''">old_name,</if>
			<if test="newName != null and newName != ''">new_name,</if>
			<if test="createBy != null">create_by,</if>
			<if test="createTime != null">create_time,</if>
			<if test="updateBy != null">update_by,</if>
			<if test="updateTime != null">update_time,</if>
			<if test="enable != null">enable,</if>
			<if test="remark != null">remark,</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="oldName != null and oldName != ''">#{oldName},</if>
			<if test="newName != null and newName != ''">#{newName},</if>
			<if test="createBy != null">#{createBy},</if>
			<if test="createTime != null">#{createTime},</if>
			<if test="updateBy != null">#{updateBy},</if>
			<if test="updateTime != null">#{updateTime},</if>
			<if test="enable != null">#{enable},</if>
			<if test="remark != null">#{remark},</if>
		</trim>
	</insert>

	<update id="updateSerialConfig" parameterType="SerialConfig">
		update bu_serial_config
		<trim prefix="SET" suffixOverrides=",">
			<if test="oldName != null and oldName != ''">old_name = #{oldName},</if>
			<if test="newName != null and newName != ''">new_name = #{newName},</if>
			<if test="createBy != null">create_by = #{createBy},</if>
			<if test="createTime != null">create_time = #{createTime},</if>
			<if test="updateBy != null">update_by = #{updateBy},</if>
			<if test="updateTime != null">update_time = #{updateTime},</if>
			<if test="enable != null">enable = #{enable},</if>
			<if test="remark != null">remark = #{remark},</if>
		</trim>
		where id = #{id}
	</update>

	<delete id="deleteSerialConfigById" parameterType="Long">
		delete from bu_serial_config where id = #{id}
	</delete>

	<delete id="deleteSerialConfigByIds" parameterType="String">
		delete from bu_serial_config where id in
		<foreach item="id" collection="array" open="(" separator="," close=")">
			#{id}
		</foreach>
	</delete>


	<select id="selectSerialConfig" parameterType="SerialConfig" resultMap="SerialConfigResult">
		<include refid="selectSerialConfigVo"/>
		<where>
			<if test="id !=null">
				and id = #{id}
			</if>
			<if test="oldName != null and oldName != ''">
				AND old_name = #{oldName}
			</if>
			<if test="newName != null and newName != ''">
				AND new_name = #{newName}
			</if>
		</where>
	</select>


</mapper>