<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snct.system.mapper.SysRestartMapper">

	<resultMap type="SysRestart" id="SysRestartResult">
		<id     property="restartId"        column="restart_id"       />
		<result property="restartType"      column="restart_type"     />
		<result property="restartName"      column="restart_name"     />
		<result property="today"      column="today"     />
		<result property="restartTime"      column="restart_time"     />
		<result property="doTime"        column="do_time"        />
		<result property="status"        column="status"        />
		<result property="createBy"      column="create_by"     />
		<result property="createTime"    column="create_time"   />
		<result property="updateBy"      column="update_by"     />
		<result property="updateTime"    column="update_time"   />
		<result property="remark"        column="remark"        />
	</resultMap>

	<sql id="selectRestartVo">
        select restart_id, restart_type, restart_name, today, restart_time,do_time, status, create_by, create_time, remark
		from sys_restart
    </sql>

	<select id="selectSysRestartList" parameterType="SysRestart" resultMap="SysRestartResult">
	    <include refid="selectRestartVo"/>
		<where>
			<if test="restartType != null and restartType != ''">
				AND restart_type like concat('%', #{restartType}, '%')
			</if>
			<if test="status != null and status != ''">
				AND status = #{status}
			</if>
			<if test="restartName != null and restartName != ''">
				AND restart_name like concat('%', #{restartName}, '%')
			</if>
		</where>
	</select>

	<select id="selectRestartAll" resultMap="SysRestartResult">
		<include refid="selectRestartVo"/>
	</select>

	<select id="selectSysRestartByRestartId" parameterType="Long" resultMap="SysRestartResult">
		<include refid="selectRestartVo"/>
		where restart_id = #{restartId}
	</select>

	<select id="checkRestartNameUnique" parameterType="String" resultMap="SysRestartResult">
		<include refid="selectRestartVo"/>
		 where restart_name=#{restartName} limit 1
	</select>
	<select id="checkRestartCodeUnique" parameterType="String" resultMap="SysRestartResult">
		<include refid="selectRestartVo"/>
		 where restart_code=#{restartCode} limit 1
	</select>

	<select id="selectActiveRestartTasks" resultMap="SysRestartResult">
		<include refid="selectRestartVo"/>
		where status = '0'
	</select>

	<update id="updateSysRestart" parameterType="sysRestart">
 		update sys_restart
 		<set>
 			<if test="restartType != null and restartType != ''">restart_type = #{restartType},</if>
 			<if test="restartName != null and restartName != ''">restart_name = #{restartName},</if>
 			<if test="today != null">today = #{today},</if>
 			<if test="restartTime != null">restart_time = #{restartTime},</if>
			<if test="doTime != null">do_time = #{doTime},</if>
 			<if test="status != null and status != ''">status = #{status},</if>
 			<if test="remark != null">remark = #{remark},</if>
 			<if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
 			update_time = sysdate()
 		</set>
 		where restart_id = #{restartId}
	</update>

 	<insert id="insertSysRestart" parameterType="SysPost" useGeneratedKeys="true" keyProperty="restartId">
 		insert into sys_restart(
 			<if test="restartId != null and restartId != 0">restart_id,</if>
 			<if test="restartType != null and restartType != ''">restart_type,</if>
 			<if test="restartName != null and restartName != ''">restart_name,</if>
 			<if test="today != null">today,</if>
 			<if test="restartTime != null">restart_time,</if>
		    <if test="doTime != null">do_time,</if>
 			<if test="status != null and status != ''">status,</if>
 			<if test="remark != null and remark != ''">remark,</if>
 			<if test="createBy != null and createBy != ''">create_by,</if>
 			create_time
 		)values(
 			<if test="restartId != null and restartId != 0">#{restartId},</if>
 			<if test="restartType != null and restartType != ''">#{restartType},</if>
 			<if test="restartName != null and restartName != ''">#{restartName},</if>
 			<if test="today != null">#{today},</if>
 			<if test="restartTime != null">#{restartTime},</if>
		    <if test="doTime != null">#{doTime},</if>
 			<if test="status != null and status != ''">#{status},</if>
 			<if test="remark != null and remark != ''">#{remark},</if>
 			<if test="createBy != null and createBy != ''">#{createBy},</if>
 			sysdate()
 		)
	</insert>

	<delete id="deleteSysRestartByRestartId" parameterType="Long">
		delete from sys_restart where restart_id = #{restartId}
	</delete>

	<delete id="deleteSysRestartByRestartIds" parameterType="Long">
 		delete from sys_restart where restart_id in
 		<foreach collection="array" item="restartIds" open="(" separator="," close=")">
 			#{restartIds}
        </foreach> 
 	</delete>

</mapper> 