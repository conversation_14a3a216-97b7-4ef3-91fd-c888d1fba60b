package com.snct.hbase.domain.device;

import com.snct.hbase.domain.HbaseBaseEntity;

/**
 * class
 *
 * <AUTHOR>
 */
public class TransferAttributeEntityHbase extends HbaseBaseEntity {

    private Long id;

    /**
     * 部门ID（岸上需要）
     */
    private Long deptId;

    private String sn;

    private String shipName;

    /**
     * 设备编号
     */
    private String deviceCode;

    /**
     * 名称
     */
    private String name;

    private String label;

    /**
     * 排序
     */
    private Integer orderNum;

    /**
     * 已选属性数组，前端传参使用
     */
    private String[] transferAttributes;

    /**
     * 优先级
     */
    private Integer cost;
    /**
     * 传输间隔(单位：秒)
     */
    private Integer compartment;

    /**
     * 传输间隔--展示
     */
    private String compartmentStr;

    /**
     * 设备名称
     * @return
     */
    private String deviceName;

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public Integer getCost() {
        return cost;
    }

    public void setCost(Integer cost) {
        this.cost = cost;
    }

    public Integer getCompartment() {
        return compartment;
    }

    public void setCompartment(Integer compartment) {
        this.compartment = compartment;
    }

    public String getCompartmentStr() {
        if (this.compartment == null) {
            return "";
        }
        if (this.compartment >= 60) {
            return (this.compartment / 60) + "分钟";
        } else {
            return this.compartment + "秒钟";
        }
    }

    public void setCompartmentStr(String compartmentStr) {
        this.compartmentStr = compartmentStr;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDeviceCode() {
        return deviceCode;
    }

    public void setDeviceCode(String deviceCode) {
        this.deviceCode = deviceCode;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public String[] getTransferAttributes() {
        return transferAttributes;
    }

    public void setTransferAttributes(String[] transferAttributes) {
        this.transferAttributes = transferAttributes;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public String getShipName() {
        return shipName;
    }

    public void setShipName(String shipName) {
        this.shipName = shipName;
    }
}
