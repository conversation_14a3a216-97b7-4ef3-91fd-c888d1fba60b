//package com.snct.web.controller.business.hbase;
//
//import com.snct.common.core.controller.BaseController;
//import com.snct.common.core.domain.AjaxResult;
//import com.snct.common.utils.SecurityUtils;
//import com.snct.hbase.domain.hbase.AmplifierHbaseVo;
//import com.snct.hbase.service.HBaseDeviceDataService;
//import com.snct.system.mapper.SysDeptMapper;
//import com.snct.common.core.domain.entity.SysDept;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import io.swagger.annotations.ApiParam;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.security.access.prepost.PreAuthorize;
//import org.springframework.web.bind.annotation.*;
//
//import java.util.List;
//import java.util.Map;
//import java.util.stream.Collectors;
//
///**
// * 功放设备数据Controller（HBase版本）
// *
// * <AUTHOR>
// * @date 2025-04-24
// */
//@Api("功放设备数据(hbase)")
//@RestController
//@RequestMapping("/hbase/devicemsg/amplifier")
//public class BuMsgAmplifierHbaseController extends BaseController {
//
//    /**
//     * 设备类型常量
//     */
//    private static final String DEVICE_TYPE = "amplifier";
//
//    @Autowired
//    private HBaseDeviceDataService deviceDataService;
//
//    @Autowired
//    private SysDeptMapper deptMapper;
//
//    /**
//     * 基于游标的分页查询接口
//     */
//    @ApiOperation("查询功放设备数据列表")
//    @PreAuthorize("@ss.hasPermi('devicemsg:amplifier:list')")
//    @GetMapping("/query")
//    public AjaxResult query(
//            @ApiParam("部门ID") @RequestParam(required = false) Long deptId,
//            @ApiParam("船舶SN") @RequestParam(required = false) String sn,
//            @ApiParam("设备ID") @RequestParam(required = false) Long deviceId,
//            @ApiParam("开始日期时间(yyyy-MM-dd HH:mm:ss)") @RequestParam(required = false) String startDateTime,
//            @ApiParam("结束日期时间(yyyy-MM-dd HH:mm:ss)") @RequestParam(required = false) String endDateTime,
//            @ApiParam("每页大小") @RequestParam(defaultValue = "10") Integer limit,
//            @ApiParam("游标(上一页最后一条记录的RowKey)") @RequestParam(required = false) String cursor) {
//
//        // 获取当前用户信息
//        Long currentUserDeptId = getDeptId();
//        Long currentUserId = SecurityUtils.getUserId();
//        boolean isAdmin = SecurityUtils.isAdmin(currentUserId);
//
//        // 非管理员只能查看自己部门及子部门的数据
//        Long effectiveDeptId = deptId;
//        if (!isAdmin) {
//            // 如果不是管理员，且指定了查询部门ID
//            if (deptId != null && !deptId.equals(currentUserDeptId)) {
//                // 获取当前用户部门的所有子部门
//                List<SysDept> childDepts = deptMapper.selectChildrenDeptById(currentUserDeptId);
//                List<Long> childDeptIds = childDepts.stream().map(SysDept::getDeptId).collect(Collectors.toList());
//
//                // 检查指定的部门ID是否在子部门列表中
//                if (!childDeptIds.contains(deptId)) {
//                    effectiveDeptId = currentUserDeptId;
//                    logger.warn("用户无权查询部门ID: {}, 已限制为当前用户部门ID: {}", deptId, currentUserDeptId);
//                }
//            } else {
//                // 如果未指定部门ID或指定的就是当前用户部门，则使用当前用户部门ID
//                effectiveDeptId = currentUserDeptId;
//            }
//        }
//
//        // 参数日志
//        logger.info("功放设备游标分页查询参数: deptId={}, sn={}, deviceId={}, 时间范围={}-{}, limit={}, cursor={}",
//                effectiveDeptId, sn, deviceId, startDateTime, endDateTime, limit, cursor);
//
//        // 查询数据
//        Map<String, Object> queryResult = deviceDataService.queryCursorPagination(
//                DEVICE_TYPE, effectiveDeptId, sn, deviceId,
//                startDateTime, endDateTime, limit, cursor, isAdmin, AmplifierHbaseVo.class);
//
//        return AjaxResult.success(queryResult);
//    }
//
//    /**
//     * 获取详情接口
//     */
//    @ApiOperation("获取功放设备数据详情")
//    @PreAuthorize("@ss.hasPermi('devicemsg:amplifier:query')")
//    @GetMapping("/detail/{rowKey}")
//    public AjaxResult getDetail(@PathVariable("rowKey") String rowKey) {
//        AmplifierHbaseVo detail = deviceDataService.getDeviceDetail(DEVICE_TYPE, rowKey, AmplifierHbaseVo.class);
//        if (detail == null) {
//            return AjaxResult.error("未找到指定的数据记录");
//        }
//        return AjaxResult.success(detail);
//    }
//}