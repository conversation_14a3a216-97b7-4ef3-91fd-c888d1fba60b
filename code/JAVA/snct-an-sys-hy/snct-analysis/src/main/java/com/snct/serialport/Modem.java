package com.snct.serialport;

public class Modem {
    private String signal;
    private String speed;
    private String sendPower;
    private String isFlag;
    private String utcTime;


    public String getUtcTime() {
        return this.utcTime;
    }

    public void setUtcTime(String utcTime) {
        this.utcTime = utcTime;
    }

    public String getSignal() {
        return this.signal;
    }

    public void setSignal(String signal) {
        this.signal = signal;
    }

    public String getSpeed() {
        return this.speed;
    }

    public void setSpeed(String speed) {
        this.speed = speed;
    }

    public String getSendPower() {
        return this.sendPower;
    }

    public void setSendPower(String sendPower) {
        this.sendPower = sendPower;
    }

    public String getIsFlag() {
        return this.isFlag;
    }

    public void setIsFlag(String isFlag) {
        this.isFlag = isFlag;
    }

    @Override
    public String toString() {
        return "Modem{" +
                "signal='" + signal + '\'' +
                ", speed='" + speed + '\'' +
                ", sendPower='" + sendPower + '\'' +
                ", isFlag='" + isFlag + '\'' +
                ", utcTime='" + utcTime + '\'' +
                '}';
    }
}