package com.snct.hbase.domain.engineroom.vo;

import com.snct.hbase.annotation.HBaseColumn;
import com.snct.hbase.annotation.HBaseTable;

/**
 * <AUTHOR>
 */
@HBaseTable(tableName = "ns1:engineroom")
public class EngineroomDataVo {

    @HBaseColumn(family = "rowkey", qualifier = "rowkey")
    private String id;

    /**
     * 机舱数据（编号,名称,符号,状态,value|编号,名称,符号,状态,value|编号,名称,符号,状态,value|...）
     */
    @HBaseColumn(family = "i", qualifier = "info")
    private  String info;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getInfo() {
        return info;
    }

    public void setInfo(String info) {
        this.info = info;
    }
}
