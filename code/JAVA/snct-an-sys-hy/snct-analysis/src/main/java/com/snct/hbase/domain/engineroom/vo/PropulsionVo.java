package com.snct.hbase.domain.engineroom.vo;


import com.snct.hbase.annotation.Excel;
import com.snct.hbase.annotation.HBaseColumn;
import com.snct.hbase.annotation.HBaseTable;
import com.snct.hbase.domain.engineroom.EngineroomData;
import com.snct.hbase.utils.AnalysisUtils;

import java.util.Map;

/**
 * 主推轴承数据
 * <AUTHOR>
 */
@HBaseTable(tableName = "ns1:propulsion")
public class PropulsionVo {
    @HBaseColumn(family = "rowkey",qualifier = "rowkey")
    String id;

    private Long time;

    private String bjTime;

    /**
     *主推4 马达自由端轴承温度  05095
     */
    @Excel(name="主推4 马达自由端轴承温度")
    @HBaseColumn(family = "i",qualifier = "mt4mndt")
    private String mt4MtrNonDrvTemp;


    /**
     *主推4 马达驱动端轴承温度  05094
     */
    @Excel(name="主推4 马达驱动端轴承温度")
    @HBaseColumn(family = "i",qualifier = "mt4mdt")
    private String mt4MtrDrvTemp;

    /**
     *主推4 推力轴承滑油温度  06024
     */
    @Excel(name="主推4 推力轴承滑油温度")
    @HBaseColumn(family = "i",qualifier = "mt4ttp")
    private String mt4ThrustTempP;

    /**
     *主推4 推力轴承前端温度  06023
     */
    @Excel(name="主推4 推力轴承前端温度")
    @HBaseColumn(family = "i",qualifier = "mt4fttp")
    private String mt4FwdThrustTempP;

    /**
     *主推4 推力块（倒车）温度  06021
     */
    @Excel(name="主推4 推力块（倒车）温度")
    @HBaseColumn(family = "i",qualifier = "mt4tpatp")
    private String mt4ThrustPadAsternTempP;

    /**
     *主推4 推力块（正车）温度  06020
     */
    @Excel(name="主推4 推力块（正车）温度")
    @HBaseColumn(family = "i",qualifier = "mt4tptp")
    private String mt4ThrustPadAheadTempP;

    /**
     *主推4 推力轴承后端温度  06022
     */
    @Excel(name="主推4 推力轴承后端温度")
    @HBaseColumn(family = "i",qualifier = "mt4attp")
    private String mt4AftThrustTempP;

    /**
     *主推4.1 中间轴承前端温度  06030
     */
    @Excel(name="主推4.1 中间轴承温度")
    @HBaseColumn(family = "i",qualifier = "mt4itp1")
    private String mt4InterTempP1;

    /**
     *主推4.2 中间轴承前端温度  06036
     */
    @Excel(name="主推4.2 中间轴承温度")
    @HBaseColumn(family = "i",qualifier = "mt4itp2")
    private String mt4InterTempP2;


    /**
     *主推5 马达自由端轴承温度  05232
     */
    @Excel(name="主推5 马达自由端轴承温度")
    @HBaseColumn(family = "i",qualifier = "mt5mndt")
    private String mt5MtrNonDrvTemp;


    /**
     *主推5 马达驱动端轴承温度  05231
     */
    @Excel(name="主推5 马达驱动端轴承温度")
    @HBaseColumn(family = "i",qualifier = "mt5mdt")
    private String mt5MtrDrvTemp;

    /**
     *主推5 推力轴承滑油温度  06049
     */
    @Excel(name="主推5 推力轴承滑油温度")
    @HBaseColumn(family = "i",qualifier = "mt5ttp")
    private String mt5ThrustTempP;

    /**
     *主推5 推力轴承前端温度  06048
     */
    @Excel(name="主推5 推力轴承前端温度")
    @HBaseColumn(family = "i",qualifier = "mt5fttp")
    private String mt5FwdThrustTempP;

    /**
     *主推5 推力块（倒车）温度  06046
     */
    @Excel(name="主推5 推力块（倒车）温度")
    @HBaseColumn(family = "i",qualifier = "mt5tpatp")
    private String mt5ThrustPadAsternTempP;

    /**
     *主推5 推力块（正车）温度  06045
     */
    @Excel(name="主推5 推力块（正车）温度")
    @HBaseColumn(family = "i",qualifier = "mt5tptp")
    private String mt5ThrustPadAheadTempP;

    /**
     *主推5 推力轴承后端温度  06047
     */
    @Excel(name="主推5 推力轴承后端温度")
    @HBaseColumn(family = "i",qualifier = "mt5attp")
    private String mt5AftThrustTempP;

    /**
     *主推5.1 中间轴承前端温度  06055
     */
    @Excel(name="主推5.1 中间轴承温度")
    @HBaseColumn(family = "i",qualifier = "mt5itp1")
    private String mt5InterTempP1;

    /**
     *主推5.2 中间轴承前端温度  06061
     */
    @Excel(name="主推5.2 中间轴承温度")
    @HBaseColumn(family = "i",qualifier = "mt5itp2")
    private String mt5InterTempP2;

    public PropulsionVo(){

    }

    public PropulsionVo(Map<String, EngineroomData> map){
        this.mt4MtrNonDrvTemp = AnalysisUtils.analysis(map.get("05095"));
        this.mt4MtrDrvTemp = AnalysisUtils.analysis(map.get("05094"));
        this.mt4ThrustTempP = AnalysisUtils.analysis(map.get("06024"));
        this.mt4FwdThrustTempP = AnalysisUtils.analysis(map.get("06023"));
        this.mt4ThrustPadAsternTempP = AnalysisUtils.analysis(map.get("06021"));
        this.mt4ThrustPadAheadTempP = AnalysisUtils.analysis(map.get("06020"));
        this.mt4AftThrustTempP = AnalysisUtils.analysis(map.get("06022"));
        this.mt4InterTempP1 = AnalysisUtils.analysis(map.get("06030"));
        this.mt4InterTempP2 = AnalysisUtils.analysis(map.get("06036"));

        this.mt5MtrNonDrvTemp = AnalysisUtils.analysis(map.get("05232"));
        this.mt5MtrDrvTemp = AnalysisUtils.analysis(map.get("05231"));
        this.mt5ThrustTempP = AnalysisUtils.analysis(map.get("06049"));
        this.mt5FwdThrustTempP = AnalysisUtils.analysis(map.get("06048"));
        this.mt5ThrustPadAsternTempP = AnalysisUtils.analysis(map.get("06046"));
        this.mt5ThrustPadAheadTempP = AnalysisUtils.analysis(map.get("06045"));
        this.mt5AftThrustTempP = AnalysisUtils.analysis(map.get("06047"));
        this.mt5InterTempP1 = AnalysisUtils.analysis(map.get("06055"));
        this.mt5InterTempP2 = AnalysisUtils.analysis(map.get("06061"));

    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Long getTime() {
        return time;
    }

    public void setTime(Long time) {
        this.time = time;
    }

    public String getBjTime() {
        return bjTime;
    }

    public void setBjTime(String bjTime) {
        this.bjTime = bjTime;
    }

    public String getMt4MtrNonDrvTemp() {
        return mt4MtrNonDrvTemp;
    }

    public void setMt4MtrNonDrvTemp(String mt4MtrNonDrvTemp) {
        this.mt4MtrNonDrvTemp = mt4MtrNonDrvTemp;
    }

    public String getMt4MtrDrvTemp() {
        return mt4MtrDrvTemp;
    }

    public void setMt4MtrDrvTemp(String mt4MtrDrvTemp) {
        this.mt4MtrDrvTemp = mt4MtrDrvTemp;
    }

    public String getMt4ThrustTempP() {
        return mt4ThrustTempP;
    }

    public void setMt4ThrustTempP(String mt4ThrustTempP) {
        this.mt4ThrustTempP = mt4ThrustTempP;
    }

    public String getMt4FwdThrustTempP() {
        return mt4FwdThrustTempP;
    }

    public void setMt4FwdThrustTempP(String mt4FwdThrustTempP) {
        this.mt4FwdThrustTempP = mt4FwdThrustTempP;
    }

    public String getMt4ThrustPadAsternTempP() {
        return mt4ThrustPadAsternTempP;
    }

    public void setMt4ThrustPadAsternTempP(String mt4ThrustPadAsternTempP) {
        this.mt4ThrustPadAsternTempP = mt4ThrustPadAsternTempP;
    }

    public String getMt4ThrustPadAheadTempP() {
        return mt4ThrustPadAheadTempP;
    }

    public void setMt4ThrustPadAheadTempP(String mt4ThrustPadAheadTempP) {
        this.mt4ThrustPadAheadTempP = mt4ThrustPadAheadTempP;
    }

    public String getMt4AftThrustTempP() {
        return mt4AftThrustTempP;
    }

    public void setMt4AftThrustTempP(String mt4AftThrustTempP) {
        this.mt4AftThrustTempP = mt4AftThrustTempP;
    }

    public String getMt4InterTempP1() {
        return mt4InterTempP1;
    }

    public void setMt4InterTempP1(String mt4InterTempP1) {
        this.mt4InterTempP1 = mt4InterTempP1;
    }

    public String getMt4InterTempP2() {
        return mt4InterTempP2;
    }

    public void setMt4InterTempP2(String mt4InterTempP2) {
        this.mt4InterTempP2 = mt4InterTempP2;
    }

    public String getMt5MtrNonDrvTemp() {
        return mt5MtrNonDrvTemp;
    }

    public void setMt5MtrNonDrvTemp(String mt5MtrNonDrvTemp) {
        this.mt5MtrNonDrvTemp = mt5MtrNonDrvTemp;
    }

    public String getMt5MtrDrvTemp() {
        return mt5MtrDrvTemp;
    }

    public void setMt5MtrDrvTemp(String mt5MtrDrvTemp) {
        this.mt5MtrDrvTemp = mt5MtrDrvTemp;
    }

    public String getMt5ThrustTempP() {
        return mt5ThrustTempP;
    }

    public void setMt5ThrustTempP(String mt5ThrustTempP) {
        this.mt5ThrustTempP = mt5ThrustTempP;
    }

    public String getMt5FwdThrustTempP() {
        return mt5FwdThrustTempP;
    }

    public void setMt5FwdThrustTempP(String mt5FwdThrustTempP) {
        this.mt5FwdThrustTempP = mt5FwdThrustTempP;
    }

    public String getMt5ThrustPadAsternTempP() {
        return mt5ThrustPadAsternTempP;
    }

    public void setMt5ThrustPadAsternTempP(String mt5ThrustPadAsternTempP) {
        this.mt5ThrustPadAsternTempP = mt5ThrustPadAsternTempP;
    }

    public String getMt5ThrustPadAheadTempP() {
        return mt5ThrustPadAheadTempP;
    }

    public void setMt5ThrustPadAheadTempP(String mt5ThrustPadAheadTempP) {
        this.mt5ThrustPadAheadTempP = mt5ThrustPadAheadTempP;
    }

    public String getMt5AftThrustTempP() {
        return mt5AftThrustTempP;
    }

    public void setMt5AftThrustTempP(String mt5AftThrustTempP) {
        this.mt5AftThrustTempP = mt5AftThrustTempP;
    }

    public String getMt5InterTempP1() {
        return mt5InterTempP1;
    }

    public void setMt5InterTempP1(String mt5InterTempP1) {
        this.mt5InterTempP1 = mt5InterTempP1;
    }

    public String getMt5InterTempP2() {
        return mt5InterTempP2;
    }

    public void setMt5InterTempP2(String mt5InterTempP2) {
        this.mt5InterTempP2 = mt5InterTempP2;
    }
}
