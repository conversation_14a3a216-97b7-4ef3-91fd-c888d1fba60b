//package com.snct.web.controller.business;
//
//import com.snct.common.annotation.Log;
//import com.snct.common.core.controller.BaseController;
//import com.snct.common.core.domain.AjaxResult;
//import com.snct.common.core.page.TableDataInfo;
//import com.snct.common.enums.BusinessType;
//import com.snct.common.utils.poi.ExcelUtil;
//import com.snct.system.domain.msg.BuMsgAmplifier;
//import com.snct.system.service.IBuMsgAmplifierService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.security.access.prepost.PreAuthorize;
//import org.springframework.web.bind.annotation.*;
//
//import javax.servlet.http.HttpServletResponse;
//import java.util.List;
//
///**
// * 功放消息Controller
// *
// * <AUTHOR>
// * @date 2025-04-24
// */
//@RestController
//@RequestMapping("/devicemsg/amplifier")
//public class BuMsgAmplifierController extends BaseController
//{
//    @Autowired
//    private IBuMsgAmplifierService buMsgAmplifierService;
//
//    /**
//     * 查询功放消息列表
//     */
//    @PreAuthorize("@ss.hasPermi('devicemsg:amplifier:list')")
//    @GetMapping("/list")
//    public TableDataInfo list(BuMsgAmplifier buMsgAmplifier)
//    {
//        startPage();
//        List<BuMsgAmplifier> list = buMsgAmplifierService.selectBuMsgAmplifierList(buMsgAmplifier);
//        return getDataTable(list);
//    }
//
//    /**
//     * 导出功放消息列表
//     */
//    @PreAuthorize("@ss.hasPermi('devicemsg:amplifier:export')")
//    @Log(title = "功放消息", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, BuMsgAmplifier buMsgAmplifier)
//    {
//        List<BuMsgAmplifier> list = buMsgAmplifierService.selectBuMsgAmplifierList(buMsgAmplifier);
//        ExcelUtil<BuMsgAmplifier> util = new ExcelUtil<BuMsgAmplifier>(BuMsgAmplifier.class);
//        util.exportExcel(response, list, "功放消息数据");
//    }
//
//    /**
//     * 获取功放消息详细信息
//     */
//    @PreAuthorize("@ss.hasPermi('devicemsg:amplifier:query')")
//    @GetMapping(value = "/{id}")
//    public AjaxResult getInfo(@PathVariable("id") Long id)
//    {
//        return success(buMsgAmplifierService.selectBuMsgAmplifierById(id));
//    }
//
//    /**
//     * 新增功放消息
//     */
//    @PreAuthorize("@ss.hasPermi('devicemsg:amplifier:add')")
//    @Log(title = "功放消息", businessType = BusinessType.INSERT)
//    @PostMapping
//    public AjaxResult add(@RequestBody BuMsgAmplifier buMsgAmplifier)
//    {
//        return toAjax(buMsgAmplifierService.insertBuMsgAmplifier(buMsgAmplifier));
//    }
//
//    /**
//     * 修改功放消息
//     */
//    @PreAuthorize("@ss.hasPermi('devicemsg:amplifier:edit')")
//    @Log(title = "功放消息", businessType = BusinessType.UPDATE)
//    @PutMapping
//    public AjaxResult edit(@RequestBody BuMsgAmplifier buMsgAmplifier)
//    {
//        return toAjax(buMsgAmplifierService.updateBuMsgAmplifier(buMsgAmplifier));
//    }
//
//    /**
//     * 删除功放消息
//     */
//    @PreAuthorize("@ss.hasPermi('devicemsg:amplifier:remove')")
//    @Log(title = "功放消息", businessType = BusinessType.DELETE)
//	@DeleteMapping("/{ids}")
//    public AjaxResult remove(@PathVariable Long[] ids)
//    {
//        return toAjax(buMsgAmplifierService.deleteBuMsgAmplifierByIds(ids));
//    }
//}
