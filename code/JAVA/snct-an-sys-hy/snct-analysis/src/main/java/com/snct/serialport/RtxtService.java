package com.snct.serialport;

import com.alibaba.fastjson.JSONObject;
import com.snct.common.constant.RedisParameter;
import com.snct.common.core.redis.RedisCache;
import com.snct.common.transfer.PackageTypeEnum;
import com.snct.common.transfer.TransferPackage;
import com.snct.common.utils.DateUtils;
import com.snct.hbase.domain.hbase.GpsHbaseVo;
import com.snct.kafka.KafkaMessage;
import com.snct.system.service.ISerialConfigService;
import com.snct.utils.HexUtil;
import com.snct.utils.HttpRequestUtil;
import gnu.io.SerialPort;
import gnu.io.SerialPortEvent;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ListOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.SetOperations;
import org.springframework.stereotype.Service;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.PrintWriter;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Set;

@Service
public class RtxtService {

    // 日志记录器
    private static final Logger logger = LoggerFactory.getLogger(RtxtService.class);

    // 北斗消息缓存键前缀
    private static final String REDIS_KEY_BD_RESULT = "result_";

    // 判断当前系统类型
    private static final boolean IS_WINDOWS = System.getProperty("os.name").toLowerCase().contains("win");

    // 串口名称映射(Linux->Windows)
    private static final String PORT_BD2_COMM = IS_WINDOWS ? "COM1" : "/dev/ttyS1";        // 北斗通信设备
    private static final String PORT_BD3_COMMAND = IS_WINDOWS ? "COM2" : "/dev/ttyS0";     // 北斗3指挥机
    private static final String PORT_DATA_FORWARD = IS_WINDOWS ? "COM3" : "/dev/ttyS5";    // 数据转发端口

    // Redis缓存超时时间(分钟)
    private static final int REDIS_TIMEOUT_MINUTES = 5;

    // HTTP转发目标地址
    private static final String HTTP_FORWARD_URL = "http://192.168.1.100:8080/api/data/receive";

    // 客户端监听工具
    @Autowired
    public ClientListernerUtil clientListernerUtil;

    // Redis缓存
    @Autowired
    private RedisCache redisCache;

    // 串口配置服务
    @Autowired
    private ISerialConfigService serialConfigService;

    // Redis模板
    @Autowired
    public RedisTemplate redisTemplate;

    // HTTP请求工具
    @Autowired
    private HttpRequestUtil httpRequestUtil;

    /**
     * 初始化并处理所有串口设备
     * 查找系统中所有可用串口，并为每个串口启动监听
     */
    public void handleAllService() {
        // 查找系统所有串口
        SerialPortUtil.findSystemAllSerialPort();
        // 清除已打开串口的映射
        SerialPortUtil.cleanOpenMap();
        // 获取所有串口名称集合
        Set<String> strings = SerialPortUtil.getSerialPortNameSets();
        // 为每个串口设置监听
        for (String serialName : strings) {
            toListenerSerialPort(serialName);
        }
    }


    /**
     * 为指定串口启动监听
     * 根据串口类型设置不同的波特率，并添加数据接收事件监听器
     *
     * 数据处理流程:
     * 1. 读取串口接收到的字节数据
     * 2. 根据不同串口类型进行不同处理
     * 3. 保存原始数据并分发到对应的处理方法
     *
     * @param serialName 串口名称
     * @return 执行结果状态码
     */
    public synchronized Integer toListenerSerialPort(final String serialName) {
        if (StringUtils.isEmpty(serialName)) {
            return 0;
        }

        // 根据不同串口设置不同波特率
        int b = 115200;  // 默认高速波特率

        if (serialName.equalsIgnoreCase(PORT_BD2_COMM)) {
            b = 115200;  // 北斗通信设备
        } else if (serialName.equalsIgnoreCase(PORT_BD3_COMMAND)) {
            b = 115200;  // 北斗3指挥机
        } else if (serialName.equalsIgnoreCase(PORT_DATA_FORWARD)) {
            b = 115200;  // 数据转发端口
        }

        // 打开串口
        final SerialPort serialPort = SerialPortUtil.openComPort(serialName, b, 8, 1, 0);
        if (serialPort == null) {
            logger.error("无法打开串口: {}", serialName);
            return 0;
        }

        // 将串口添加到已打开串口映射中
        SerialPortUtil.addOpenPort(serialName, serialPort);

        // 为串口设置监听器，处理接收到的数据
        SerialPortUtil.setListenerToSerialPort(serialPort, arg0 -> {
            // 当有数据到达时
            if (arg0.getEventType() == SerialPortEvent.DATA_AVAILABLE) {
                // 读取串口数据
                byte[] bytes = SerialPortUtil.readData(serialPort, 20);
                Long time = System.currentTimeMillis();

                // 处理北斗通信设备数据
                if (serialName.equalsIgnoreCase(PORT_BD2_COMM)) {
                    String msg = new String(bytes, StandardCharsets.UTF_8);
                    logger.info("----指挥机数据-{}", msg);
                    // 检查是否是北斗报文
                    if (!msg.startsWith("$BDTXR")) {
                        return;
                    }
                    // 保存接收到的数据
                    getReceiveMessage(serialName, msg, time, 99, "099A");
                    // 处理北斗数据
                    dataAction(msg);
                    return;
                }

                // 处理北斗3指挥机数据
                if (serialName.equalsIgnoreCase(PORT_BD3_COMMAND)) {
                    String msg2 = new String(bytes, StandardCharsets.UTF_8);
                    // 检查是否是北斗3报文
                    if (!msg2.startsWith("$BDTCI")) {
                        return;
                    }
                    String msg3 = msg2.split("\\s+")[0];

                    logger.info("----北斗3指挥机数据-{}", msg3);
                    // 保存接收到的数据
                    getReceiveMessage(serialName, msg3, time, 96, "096A");
                    // 处理北斗3数据
                    dataAction3(msg3);
                    return;
                }

            }
        });

        return 1;
    }


    /**
     * 处理北斗通信设备数据
     * 解析并处理北斗设备发送的消息，支持分片消息的拼接
     *
     * 处理规则:
     * 1. 分片消息处理:
     *    - 检查消息是否有"C"结束标记
     *    - 分片消息存入Redis缓存，等待后续分片
     *    - 收到结束分片(含"C"标记)时，合并所有分片并处理
     * 2. Redis缓存键格式: "result_" + 卡号 + "_" + 消息标识前6位
     *
     * @param msg 北斗消息字符串，格式为: $BDTXR,xx,卡号,xx,xx,数据内容,...
     */
    public synchronized void dataAction(String msg) {
        logger.info("进来的数据-----{}", msg);
        // 分割消息字段
        String[] mss = msg.split(",", -1);

        // 构建Redis缓存键
        String redisKey = REDIS_KEY_BD_RESULT + mss[2] + "_" + mss[5].substring(0, 6);

        // 从Redis获取之前缓存的部分消息
        String rs = redisCache.getCacheObject(redisKey);
        logger.info("Redis缓存数据-----{}", rs);

        String msgRs = mss[5].trim();
        logger.info("消息内容-----{}", msgRs);

        // 处理多部分消息
        if (StringUtils.isNotBlank(rs)) {
            // 如果是消息结束标记C，则拼接完整消息并处理
            if (msgRs.substring(msgRs.length() - 4, msgRs.length() - 3).equalsIgnoreCase("C")) {
                // 分析完整消息
                analysis(rs + msgRs.substring(7, msgRs.length() - 3), mss[2]);
                // 清除Redis缓存
                redisCache.deleteObject(redisKey);
                return;
            } else {
                // 否则继续拼接消息并缓存
                redisCache.setCacheObject(redisKey, rs + msgRs.substring(7, msgRs.length() - 3));
                return;
            }
        }

        logger.info("消息结束标记检查-----{}", msgRs.substring(msgRs.length() - 4, msgRs.length() - 3));
        // 处理单部分消息
        if (msgRs.substring(msgRs.length() - 4, msgRs.length() - 3).equalsIgnoreCase("C")) {
            String result = msgRs.substring(0, msgRs.length() - 3);
            logger.info("完整消息内容-----{}", result);
            // 检查是否是空消息
            if (result.length() == 11 || result.length() == 12) {
                logger.info("接收到空消息---{}", result);
                return;
            } else {
                logger.info("开始分析消息-----{}", result.substring(result.length() - 2, result.length() - 1));
                // 分析消息内容
                analysis(result, mss[2]);
                return;
            }
        }
        // 缓存部分消息
        redisCache.setCacheObject(redisKey, msgRs.substring(0, msgRs.length() - 3));
    }

    /**
     * 处理北斗3通信设备数据
     * 解析并处理北斗3设备发送的消息，支持分片消息的拼接
     *
     * 处理规则:
     * 1. 分片消息处理:
     *    - 检查消息是否有"C"结束标记
     *    - 分片消息存入Redis缓存，等待后续分片
     *    - 收到结束分片(含"C"标记)时，合并所有分片并处理
     * 2. Redis缓存键格式: REDIS_KEY_BD_RESULT + 设备标识 + "_" + 消息标识前6位
     *
     * @param msg 北斗3消息字符串，格式为: $BDTCI,设备标识,xx,xx,xx,xx,xx,数据内容,...
     */
    public synchronized void dataAction3(String msg) {
        // 分割消息字段
        String[] mss = msg.split(",", -1);

        // 构建Redis缓存键
        String redisKey = REDIS_KEY_BD_RESULT + mss[1] + "_" + mss[7].substring(0, 6);

        // 从Redis获取之前缓存的部分消息
        String rs = redisCache.getCacheObject(redisKey);
        logger.info("Redis缓存数据-----{}", rs);

        String msgRs = mss[7].trim();
        logger.info("消息内容-----{}", msgRs);

        // 处理多部分消息
        if (StringUtils.isNotBlank(rs)) {
            // 如果是消息结束标记C，则拼接完整消息并处理
            if (msgRs.substring(msgRs.length() - 4, msgRs.length() - 3).equalsIgnoreCase("C")) {
                // 分析完整消息
                analysis3(rs + msgRs.substring(7, msgRs.length() - 3), mss[1]);
                // 清除Redis缓存
                redisCache.deleteObject(redisKey);
                return;
            } else {
                // 否则继续拼接消息并缓存
                redisCache.setCacheObject(redisKey, rs + msgRs.substring(7, msgRs.length() - 3));
                return;
            }
        }

        logger.info("消息结束标记检查-----{}", msgRs.substring(msgRs.length() - 4, msgRs.length() - 3));
        // 处理单部分消息
        if (msgRs.substring(msgRs.length() - 4, msgRs.length() - 3).equalsIgnoreCase("C")) {
            String result = msgRs.substring(0, msgRs.length() - 3);
            logger.info("完整消息内容-----{}", result);
            // 分析消息内容
            analysis3(result, mss[1]);
        } else {
            // 缓存部分消息
            redisCache.setCacheObject(redisKey, msgRs.substring(0, msgRs.length() - 3));
        }
    }


    /**
     * 分析北斗通信设备发送的数据
     * 解析GPS和气象站数据并转发
     *
     * 数据解析规则:
     * 1. 字符替换: A→逗号，B→小数点，D→负号
     * 2. 字段解析:
     *    - 字段0: UTC时间
     *    - 字段1: 纬度(最后一位: 1=N北, 3=S南)
     *    - 字段2: 经度(最后一位: 2=E东, 4=W西)
     *    - 字段3-11: 气象站数据(风速,风向,温度,湿度,露点,气压等)
     * 3. 输出数据格式:
     *    - GPS数据: 时间,纬度,纬度半球,经度,经度半球
     *    - 气象站数据: 时间,风速,风向,温度,湿度,露点温度,大气压,场压,修正海压,露点差
     *
     * @param result 完整消息内容
     * @param snId 设备ID
     * @return 处理结果状态码
     */
    private int analysis(String result, String snId) {
        // 替换特殊字符并分割字段
        String[] res = result.substring(0, result.length() - 1).replace("A", ",").replace("B", ".").replace("D", "-").split(",", -1);
        Long time = System.currentTimeMillis();

        // 获取UTC时间、纬度和经度
        String utcTime = res[0].trim();
        String latitude = res[1].trim();
        String longitude = res[2].trim();

        // 创建GPS数据对象
        GpsHbaseVo gpsHbaseVo = new GpsHbaseVo();
        logger.info("====BD2===={}", JSONObject.toJSONString(gpsHbaseVo));
        gpsHbaseVo.setUtcTime(utcTime);

        String latitudeRes = "";
        String longitudeRes = "";

        // 处理纬度数据
        if (StringUtils.isNotBlank(latitude)) {
            gpsHbaseVo.setLatitude(latitude.substring(0, latitude.length() - 1));
            if (StringUtils.isNotBlank(latitude.substring(latitude.length() - 1, latitude.length()))) {
                if (latitude.substring(latitude.length() - 1, latitude.length()).equalsIgnoreCase("1")) {
                    gpsHbaseVo.setLatitudeHemisphere("N");
                } else if (latitude.substring(latitude.length() - 1, latitude.length()).equalsIgnoreCase("3")) {
                    gpsHbaseVo.setLatitudeHemisphere("S");
                }
            }
            // 将纬度转换为度数格式
            BigDecimal lat = new BigDecimal(gpsHbaseVo.getLatitude());
            latitudeRes = lat.divide(new BigDecimal(100)).doubleValue() + "";
        } else {
            gpsHbaseVo.setLatitudeHemisphere("");
        }

        // 处理经度数据
        if (StringUtils.isNotBlank(longitude)) {
            gpsHbaseVo.setLongitude(longitude.substring(0, longitude.length() - 1));
            if (StringUtils.isNotBlank(longitude.substring(longitude.length() - 1, longitude.length()))) {
                if (longitude.substring(longitude.length() - 1, longitude.length()).equalsIgnoreCase("2")) {
                    gpsHbaseVo.setLongitudeHemisphere("E");
                } else if (longitude.substring(longitude.length() - 1, longitude.length()).equalsIgnoreCase("4")) {
                    gpsHbaseVo.setLongitudeHemisphere("W");
                }
            }
            // 将经度转换为度数格式
            BigDecimal lon = new BigDecimal(gpsHbaseVo.getLongitude());
            longitudeRes = lon.divide(new BigDecimal(100)).doubleValue() + "";
        } else {
            gpsHbaseVo.setLongitudeHemisphere("");
        }

        // 检查是否有有效的GPS数据
        if (!StringUtils.isBlank(utcTime) || !StringUtils.isBlank(latitudeRes) ||
                !StringUtils.isBlank(gpsHbaseVo.getLatitudeHemisphere()) || !StringUtils.isBlank(longitudeRes) ||
                !StringUtils.isBlank(gpsHbaseVo.getLongitudeHemisphere())) {

            // 处理UTC时间格式
            if (StringUtils.isNotBlank(utcTime)) {
                Calendar calendar = Calendar.getInstance();
                if (utcTime.substring(0, 2).compareTo("23") == 0 && calendar.get(Calendar.HOUR_OF_DAY) == 8) {
                    calendar.add(Calendar.DAY_OF_MONTH, -1);
                } else {
                    calendar.add(Calendar.HOUR_OF_DAY, -8);
                }
                calendar.set(Calendar.HOUR_OF_DAY, Integer.parseInt(utcTime.substring(0, 2)));
                calendar.set(Calendar.MINUTE, Integer.parseInt(utcTime.substring(2, 4)));
                calendar.set(Calendar.SECOND, Integer.parseInt(utcTime.substring(4, 6)));
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                utcTime = sdf.format(calendar.getTime());
            } else {
                Calendar calendar2 = Calendar.getInstance();
                calendar2.add(Calendar.HOUR_OF_DAY, -8);
                SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                utcTime = sdf2.format(calendar2.getTime());
            }

            // 构建GPS数据字符串
            String gps = utcTime + "," + latitudeRes + "," + gpsHbaseVo.getLatitudeHemisphere() + "," + longitudeRes + "," + gpsHbaseVo.getLongitudeHemisphere();

            // 创建Kafka消息并发送
            KafkaMessage gpsKafka = new KafkaMessage(32, "032A", gps, 10, time);
            gpsKafka.setSn(snId);
            sendSocketToClient(gpsKafka);
        }

        // 创建气象站数据对象
        AwsInHbaseVo1 awsHbaseVo = new AwsInHbaseVo1();
        // 从结果数组中提取气象站数据
        String relativeWindSpeed = res[3].trim();  // 相对风速
        String relativeWind = res[4].trim();       // 相对风向
        String airTemperature = res[5].trim();     // 空气温度
        String humidity = res[6].trim();           // 湿度
        String pointTem = res[7].trim();           // 露点温度
        String pressure = res[8].trim();           // 大气压力
        String qfe = res[9].trim();                // 场压
        String qnh = res[10].trim();               // 修正海压
        String dp = res[11].trim();                // 露点差

        // 设置气象站数据对象属性
        awsHbaseVo.setUtcTime(utcTime);
        awsHbaseVo.setRelativeWindSpeed(relativeWindSpeed);
        awsHbaseVo.setRelativeWind(relativeWind);
        awsHbaseVo.setAirTemperature(airTemperature);
        awsHbaseVo.setHumidity(humidity);
        awsHbaseVo.setPointTem(pointTem);
        awsHbaseVo.setPressure(pressure);
        awsHbaseVo.setQfe(qfe);
        awsHbaseVo.setQnh(qnh);
        awsHbaseVo.setDp(dp);

        logger.info("----AWS----{}", JSONObject.toJSONString(awsHbaseVo));

        // 构建气象站数据字符串
        String awsData = relativeWindSpeed + "," + relativeWind + "," + airTemperature + "," + humidity + "," + pointTem + "," + pressure + "," + qfe + "," + qnh + "," + dp;
        String awsMsg = utcTime + "," + awsData;

        // 检查是否有有效的气象站数据
        if (!awsData.equalsIgnoreCase(",,,,,,,,")) {
            // 创建Kafka消息并发送
            KafkaMessage awsKafka = new KafkaMessage(38, "038A", awsMsg, 10, time);
            awsKafka.setSn(snId);
            sendSocketToClient(awsKafka);
            return 1;
        }
        return 1;
    }

    /**
     * 分析北斗3通信设备发送的数据
     * 比analysis方法功能更全面，除GPS和气象站数据外，还处理调制解调器、功放和PDU设备数据
     *
     * 数据解析规则:
     * 1. 字符替换: A→逗号，B→小数点，D→负号
     * 2. 字段解析:
     *    - 字段0-11: GPS和气象站数据
     *    - 字段12-15: 调制解调器数据(信号,速率,功率,状态)
     *    - 字段16-19: 功放数据(衰减,温度,输出功率,状态)
     *    - 字段20-50: PDU数据(电源参数和8个插座的详细信息)
     *    - 字段51-56: 姿态仪数据
     * 3. 输出数据格式:
     *    - GPS数据: 时间,纬度,纬度半球,经度,经度半球
     *    - 气象站数据: 时间,风速,风向,温度,湿度,露点温度,大气压,场压,修正海压,露点差
     *    - 调制解调器数据: 时间,信号强度,速率,发送功率,状态标志
     *    - 功放数据: 时间,衰减,温度,输出功率,BUC状态
     *    - PDU数据: 时间,电流,管理电压,电压,有功功率,无功功率,视在功率,功率因数,插座1编号,插座1电流,...
     *    - 姿态仪数据:
     *
     * @param result 完整消息内容
     * @param snId 设备ID
     * @return 处理结果状态码
     */
    private int analysis3(String result, String snId) {
        // 替换特殊字符并分割字段
        String[] res = result.substring(0, result.length() - 1).replace("A", ",").replace("B", ".").replace("D", "-").split(",", -1);
        Long time = System.currentTimeMillis();

        // 获取UTC时间、纬度和经度
        String utcTime = res[0].trim();
        String latitude = res[1].trim();
        String longitude = res[2].trim();

        // 创建GPS数据对象并处理（与analysis方法类似）
        GpsHbaseVo gpsHbaseVo = new GpsHbaseVo();
        gpsHbaseVo.setUtcTime(utcTime);

        String latitudeRes = "";
        String longitudeRes = "";

        // 处理纬度数据
        if (StringUtils.isNotBlank(latitude)) {
            gpsHbaseVo.setLatitude(latitude.substring(0, latitude.length() - 1));
            if (StringUtils.isNotBlank(latitude.substring(latitude.length() - 1, latitude.length()))) {
                if (latitude.substring(latitude.length() - 1, latitude.length()).equalsIgnoreCase("1")) {
                    gpsHbaseVo.setLatitudeHemisphere("N");
                } else if (latitude.substring(latitude.length() - 1, latitude.length()).equalsIgnoreCase("3")) {
                    gpsHbaseVo.setLatitudeHemisphere("S");
                }
            }
            BigDecimal lat = new BigDecimal(gpsHbaseVo.getLatitude());
            latitudeRes = lat.divide(new BigDecimal(100)).doubleValue() + "";
        } else {
            gpsHbaseVo.setLatitudeHemisphere("");
        }

        // 处理经度数据
        if (StringUtils.isNotBlank(longitude)) {
            gpsHbaseVo.setLongitude(longitude.substring(0, longitude.length() - 1));
            if (StringUtils.isNotBlank(longitude.substring(longitude.length() - 1, longitude.length()))) {
                if (longitude.substring(longitude.length() - 1, longitude.length()).equalsIgnoreCase("2")) {
                    gpsHbaseVo.setLongitudeHemisphere("E");
                } else if (longitude.substring(longitude.length() - 1, longitude.length()).equalsIgnoreCase("4")) {
                    gpsHbaseVo.setLongitudeHemisphere("W");
                }
            }
            BigDecimal lon = new BigDecimal(gpsHbaseVo.getLongitude());
            longitudeRes = lon.divide(new BigDecimal(100)).doubleValue() + "";
        } else {
            gpsHbaseVo.setLongitudeHemisphere("");
        }

        // 检查是否有有效的GPS数据并处理
        if (!StringUtils.isBlank(utcTime) || !StringUtils.isBlank(latitudeRes) ||
                !StringUtils.isBlank(gpsHbaseVo.getLatitudeHemisphere()) || !StringUtils.isBlank(longitudeRes) ||
                !StringUtils.isBlank(gpsHbaseVo.getLongitudeHemisphere())) {

            // 处理UTC时间格式
            if (StringUtils.isNotBlank(utcTime)) {
                Calendar calendar = Calendar.getInstance();
                if (utcTime.substring(0, 2).compareTo("23") == 0 && calendar.get(Calendar.HOUR_OF_DAY) == 8) {
                    calendar.add(Calendar.DAY_OF_MONTH, -1);
                } else {
                    calendar.add(Calendar.HOUR_OF_DAY, -8);
                }
                calendar.set(Calendar.HOUR_OF_DAY, Integer.parseInt(utcTime.substring(0, 2)));
                calendar.set(Calendar.MINUTE, Integer.parseInt(utcTime.substring(2, 4)));
                calendar.set(Calendar.SECOND, Integer.parseInt(utcTime.substring(4, 6)));
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                utcTime = sdf.format(calendar.getTime());
            } else {
                Calendar calendar2 = Calendar.getInstance();
                calendar2.add(Calendar.HOUR_OF_DAY, -8);
                SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                utcTime = sdf2.format(calendar2.getTime());
            }

            logger.info("====BD3===={}", JSONObject.toJSONString(gpsHbaseVo));

            // 构建GPS数据字符串
            String gps = utcTime + "," + latitudeRes + "," + gpsHbaseVo.getLatitudeHemisphere() + "," + longitudeRes + "," + gpsHbaseVo.getLongitudeHemisphere();

            KafkaMessage gpsKafka = new KafkaMessage(32, "032B", gps, 10, time);
            gpsKafka.setSn(snId);
            sendSocketToClient(gpsKafka);
        }

        // 创建气象站数据对象
        AwsInHbaseVo1 awsHbaseVo1 = new AwsInHbaseVo1();
        String relativeWindSpeed = res[3].trim();
        String relativeWind = res[4].trim();
        String airTemperature = res[5].trim();
        String humidity = res[6].trim();
        String pointTem = res[7].trim();
        String pressure = res[8].trim();
        String qfe = res[9].trim();
        String qnh = res[10].trim();
        String dp = res[11].trim();
        awsHbaseVo1.setInitialTime(utcTime);
        awsHbaseVo1.setRelativeWindSpeed(relativeWindSpeed);
        awsHbaseVo1.setRelativeWind(relativeWind);
        awsHbaseVo1.setAirTemperature(airTemperature);
        awsHbaseVo1.setHumidity(humidity);
        awsHbaseVo1.setPointTem(pointTem);
        awsHbaseVo1.setPressure(pressure);
        awsHbaseVo1.setQfe(qfe);
        awsHbaseVo1.setQnh(qnh);
        awsHbaseVo1.setDp(dp);

        logger.info("----AWS----{}", JSONObject.toJSONString(awsHbaseVo1));

        String awsData = relativeWindSpeed + "," + relativeWind + "," + airTemperature + "," + humidity + "," + pointTem + "," + pressure + "," + qfe + "," + qnh + "," + awsHbaseVo1.getDp();
        String awsMsg = utcTime + "," + awsData;
        if (!awsData.equalsIgnoreCase(",,,,,,,,")) {
            KafkaMessage awsKafka = new KafkaMessage(38, "038A", awsMsg, 10, time);
            awsKafka.setSn(snId);
            sendSocketToClient(awsKafka);
        }

        // 处理调制解调器数据
        if (res.length > 12) {
            Modem modem = new Modem();
            modem.setSignal(res[12].trim());       // 信号强度
            modem.setSpeed(res[13].trim());        // 速率
            modem.setSendPower(res[14].trim());    // 发送功率
            modem.setIsFlag(res[15].trim());       // 状态标志
            modem.setUtcTime(utcTime);

            logger.info("----Modem----{}", JSONObject.toJSONString(modem));

            // 构建调制解调器数据字符串
            String modemData = modem.getSignal() + "," + modem.getSpeed() + "," + modem.getSendPower() + "," + modem.getIsFlag();
            String modemMsg = utcTime + "," + modemData;

            // 检查是否有有效的调制解调器数据
            if (!modemData.equalsIgnoreCase(",,,")) {
                KafkaMessage modemKafka = new KafkaMessage(66, "066A", modemMsg, 10, time);
                modemKafka.setSn(snId);
                sendSocketToClient(modemKafka);
            }
        }

        // 处理功放数据（如果存在）
        if (res.length > 16) {
            Amplifier amplifier = new Amplifier();
            amplifier.setDecay(res[16].trim());      // 衰减
            amplifier.setTemp(res[17].trim());       // 温度
            amplifier.setOutPower(res[18].trim());   // 输出功率
            amplifier.setBucStatus(res[19].trim());  // BUC状态
            amplifier.setUtcTime(utcTime);

            logger.info("----功放----{}", JSONObject.toJSONString(amplifier));

            // 构建功放数据字符串
            String amplifierData = amplifier.getDecay() + "," + amplifier.getTemp() + "," + amplifier.getOutPower() + "," + amplifier.getBucStatus();
            String amplifierMsg = utcTime + "," + amplifierData;

            // 检查是否有有效的功放数据
            if (!amplifierData.equalsIgnoreCase(",,,")) {
                KafkaMessage amplifierKafka = new KafkaMessage(68, "068A", amplifierMsg, 10, time);
                amplifierKafka.setSn(snId);
                sendSocketToClient(amplifierKafka);
            }
        }

        // 处理PDU（电源分配单元）数据（如果存在）
        if (res.length > 20) {
            SocketEntity socketEntity = new SocketEntity();
            socketEntity.setUtcTime(utcTime);

            // 设置PDU主要参数
            socketEntity.setElectric(res[20].trim().isEmpty() ? "" : String.valueOf(Integer.parseInt(res[20].trim())/10.0)); // 电流(A)
            socketEntity.setManage(res[21].trim().isEmpty() ? "" : String.valueOf(Integer.parseInt(res[21].trim())/(Math.pow(2,16)*10.0))); // 电能(kWh)
            socketEntity.setVoltage(res[22].trim().isEmpty() ? "" : String.valueOf(Integer.parseInt(res[22].trim())/10.0)); // 电压(V)
            socketEntity.setYesPwoer(res[23].trim().isEmpty() ? "" : String.valueOf(Integer.parseInt(res[23].trim())/10.0)); // 有功功率(kW)
            socketEntity.setNoPwoer(res[24].trim().isEmpty() ? "" : String.valueOf(Integer.parseInt(res[24].trim())/10.0)); // 无功功率(kW)
            socketEntity.setSeePwoer(res[25].trim().isEmpty() ? "" : String.valueOf(Integer.parseInt(res[25].trim())/10.0)); // 视在功率(kW)
            socketEntity.setPowerParam(res[26].trim().isEmpty() ? "" : String.valueOf(Integer.parseInt(res[26].trim())/10.0)); // 功率参数

            // 设置8个插座的状态信息
            socketEntity.setNumber1("1");
            socketEntity.setElectric1(res[27].trim().isEmpty() ? "" : String.valueOf(Integer.parseInt(res[27].trim())/10.0)); // 电流(A)
            socketEntity.setPower1(res[28].trim().isEmpty() ? "" : String.valueOf(Integer.parseInt(res[28].trim())/10.0)); // 功率(kW)
            socketEntity.setStatus1(res[29].trim());

            socketEntity.setNumber2("2");
            socketEntity.setElectric2(res[30].trim().isEmpty() ? "" : String.valueOf(Integer.parseInt(res[30].trim())/10.0));
            socketEntity.setPower2(res[31].trim().isEmpty() ? "" : String.valueOf(Integer.parseInt(res[31].trim())/10.0));
            socketEntity.setStatus2(res[32].trim());

            socketEntity.setNumber3("3");
            socketEntity.setElectric3(res[33].trim().isEmpty() ? "" : String.valueOf(Integer.parseInt(res[33].trim())/10.0));
            socketEntity.setPower3(res[34].trim().isEmpty() ? "" : String.valueOf(Integer.parseInt(res[34].trim())/10.0));
            socketEntity.setStatus3(res[35].trim());

            socketEntity.setNumber4("4");
            socketEntity.setElectric4(res[36].trim().isEmpty() ? "" : String.valueOf(Integer.parseInt(res[36].trim())/10.0));
            socketEntity.setPower4(res[37].trim().isEmpty() ? "" : String.valueOf(Integer.parseInt(res[37].trim())/10.0));
            socketEntity.setStatus4(res[38].trim());

            socketEntity.setNumber5("5");
            socketEntity.setElectric5(res[39].trim().isEmpty() ? "" : String.valueOf(Integer.parseInt(res[39].trim())/10.0));
            socketEntity.setPower5(res[40].trim().isEmpty() ? "" : String.valueOf(Integer.parseInt(res[40].trim())/10.0));
            socketEntity.setStatus5(res[41].trim());

            socketEntity.setNumber6("6");
            socketEntity.setElectric6(res[42].trim().isEmpty() ? "" : String.valueOf(Integer.parseInt(res[42].trim())/10.0));
            socketEntity.setPower6(res[43].trim().isEmpty() ? "" : String.valueOf(Integer.parseInt(res[43].trim())/10.0));
            socketEntity.setStatus6(res[44].trim());

            socketEntity.setNumber7("7");
            socketEntity.setElectric7(res[45].trim().isEmpty() ? "" : String.valueOf(Integer.parseInt(res[45].trim())/10.0));
            socketEntity.setPower7(res[46].trim().isEmpty() ? "" : String.valueOf(Integer.parseInt(res[46].trim())/10.0));
            socketEntity.setStatus7(res[47].trim());

            socketEntity.setNumber8("8");
            socketEntity.setElectric8(res[48].trim().isEmpty() ? "" : String.valueOf(Integer.parseInt(res[48].trim())/10.0));
            socketEntity.setPower8(res[49].trim().isEmpty() ? "" : String.valueOf((Integer.parseInt(res[49].trim())/10.0)));
            socketEntity.setStatus8(res[50].trim());

            logger.info("----PDU----{}", JSONObject.toJSONString(socketEntity));

            // 构建PDU数据字符串（包含所有插座信息）
            String pduData = socketEntity.getElectric() + "," + socketEntity.getManage() + "," + socketEntity.getVoltage() + "," + socketEntity.getYesPwoer() + "," + socketEntity.getNoPwoer() + "," + socketEntity.getSeePwoer() + "," + socketEntity.getPowerParam() + "," + socketEntity.getNumber1() + "," + socketEntity.getElectric1() + "," + socketEntity.getPower1() + "," + socketEntity.getStatus1() + "," + socketEntity.getNumber2() + "," + socketEntity.getElectric2() + "," + socketEntity.getPower2() + "," + socketEntity.getStatus2() + "," + socketEntity.getNumber3() + "," + socketEntity.getElectric3() + "," + socketEntity.getPower3() + "," + socketEntity.getStatus3() + "," + socketEntity.getNumber4() + "," + socketEntity.getElectric4() + "," + socketEntity.getPower4() + "," + socketEntity.getStatus4() + "," + socketEntity.getNumber5() + "," + socketEntity.getElectric5() + "," + socketEntity.getPower5() + "," + socketEntity.getStatus5() + "," + socketEntity.getNumber6() + "," + socketEntity.getElectric6() + "," + socketEntity.getPower6() + "," + socketEntity.getStatus6() + "," + socketEntity.getNumber7() + "," + socketEntity.getElectric7() + "," + socketEntity.getPower7() + "," + socketEntity.getStatus7() + "," + socketEntity.getNumber8() + "," + socketEntity.getElectric8() + "," + socketEntity.getPower8() + "," + socketEntity.getStatus8();
            String pduMsg = utcTime + "," + pduData;

            // 检查是否有有效的PDU数据
            if (!pduData.equalsIgnoreCase(",,,,,,,1,,,,2,,,,3,,,,4,,,,5,,,,6,,,,7,,,,8,,,")) {
                KafkaMessage pduKafka = new KafkaMessage(67, "067A", pduMsg, 10, time);
                pduKafka.setSn(snId);
                sendSocketToClient(pduKafka);
            }
        }

        // 处理姿态仪数据
        if (res.length > 51) {
            AttitudeHbaseVo1 attitudeHbaseVo = new AttitudeHbaseVo1();
            attitudeHbaseVo.setRolling(res[51].trim());  // 横滚
            attitudeHbaseVo.setPitch(res[52].trim());    // 俯仰
            attitudeHbaseVo.setHeading(res[53].trim());  // 航向
            attitudeHbaseVo.setLon(res[54].trim());      // 经度
            attitudeHbaseVo.setLat(res[55].trim());      // 纬度
            attitudeHbaseVo.setHeight(res[56].trim());   // 高度
            // 采集端没有设置该字段，暂时设为空
            if (res.length > 57) {
                attitudeHbaseVo.setStationName(res[57].trim());
            }else {
                attitudeHbaseVo.setStationName("");
            }
            attitudeHbaseVo.setInitialTime(utcTime);
            logger.info("----姿态----{}", JSONObject.toJSONString(attitudeHbaseVo));
            String attitudeData = attitudeHbaseVo.getRolling() + "," + attitudeHbaseVo.getPitch()+ "," + attitudeHbaseVo.getHeight() + "," + attitudeHbaseVo.getHeading() + "," + attitudeHbaseVo.getStationName() + "," + attitudeHbaseVo.getLon() + "," + attitudeHbaseVo.getLat();
            String attitudeMsg = utcTime + "," + attitudeData;
            if (!attitudeData.equalsIgnoreCase(",,,,,,")) {
                KafkaMessage attitudeKafka = new KafkaMessage(33, "033A", attitudeMsg, 10, time);
                attitudeKafka.setSn(snId);
                sendSocketToClient(attitudeKafka);
                return 1;
            }
        }
        return 1;
    }

    /**
     * 发送数据到客户端
     */
    private void sendSocketToClient(KafkaMessage kafkaMessage) {
        Long recordNum = System.currentTimeMillis() / 1000;
        TransferPackage transferPackage = new TransferPackage(recordNum, kafkaMessage.getInitialTime(),
                PackageTypeEnum.DEVICE_DATA.getValue(), 0, kafkaMessage.getMsg(),
                kafkaMessage.getType(), kafkaMessage.getCode());
        transferPackage.setSn(kafkaMessage.getSn());

        SetOperations<String, Integer> opsForSet = redisTemplate.opsForSet();
        opsForSet.add(RedisParameter.SHIP_REPAIR_DATA_SET, transferPackage.getCommandNum());
        saveData2Redis(transferPackage, kafkaMessage.getCost());

        if (!kafkaMessage.getSn().equalsIgnoreCase("8888888")) {
            sendMsg(transferPackage, PORT_DATA_FORWARD);
        }
    }

    /**
     * 发送消息到串口
     */
    public synchronized void sendMsg(TransferPackage transferPackage, String com2) {
        String msg = transferPackage.getSn() + "," + transferPackage.getDeviceCode() + "," +
                transferPackage.getDeviceType() + "&" + transferPackage.getMessage();
        SerialPortUtil.sendDataToComPort(SerialPortUtil.getOpenPortByName(com2), msg.getBytes());
        logger.info("往串口[{}]推送数据---{}",com2, msg);

        try {
            Thread.sleep(50L);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    /**
     * 保存数据到Redis
     */
    public void saveData2Redis(TransferPackage transferPackage, Integer cost) {
        ListOperations<String, TransferPackage> opsForList = redisTemplate.opsForList();
        String key = RedisParameter.SHIP_DO_SEND_LIST_COST + cost;
        opsForList.rightPush(key, transferPackage);
    }

    /**
     * 获取接收消息并保存到文件
     *
     * 处理流程:
     * 1. 清理数据: 移除特殊标记、换行和制表符
     * 2. 创建Kafka消息: 包含类型、代码、内容和时间戳
     * 3. 保存到文件: 按日期和消息代码组织存储
     *
     * @param serialName 串口名称
     * @param receiveMessage 接收到的消息
     * @param time 时间戳
     * @param type 消息类型(32=GPS, 38=气象站等)
     * @param code 消息代码(032A, 032B, 038A等)
     * @return 处理后的Kafka消息对象
     */
    public static KafkaMessage getReceiveMessage(String serialName, String receiveMessage, Long time, Integer type, String code) {
        // 移除消息中的特殊标记
        if (receiveMessage.contains("@@")) {
            receiveMessage = receiveMessage.substring(8);
        }
        // 移除消息中的换行和制表符
        if (receiveMessage.contains("\n\t")) {
            receiveMessage = receiveMessage.replace("\n", "").replace("\t", "");
        }
        // 创建Kafka消息对象
        KafkaMessage kafkaMessage = new KafkaMessage(type, code, receiveMessage, 10, time);
        // 保存到文本文件
        save2Txt(kafkaMessage);
        return kafkaMessage;
    }

    /**
     * 将消息保存到文本文件
     * 按日期和消息代码组织文件结构
     *
     * 文件结构:
     * - 基础路径/SNCT-消息代码/日期.txt
     * - 每条消息前添加时间戳标记: @&yyyy-MM-dd HH:mm:ss.SSS&@
     *
     * @param message Kafka消息对象
     */
    public static void save2Txt(KafkaMessage message) {
        String sourceDataPath = IS_WINDOWS ? "D:/snct/sourceData" : "/home/<USER>/sourceData";
        String directory = sourceDataPath + "/SNCT-" + message.getCode();
        File file = new File(directory);
        if (!file.exists()) {
            file.mkdirs();
        }

        // 创建文件路径：目录 + 当前日期.txt
        String fileFullPath = directory + "/" + DateUtils.getDate() + ".txt";
        try {
            // 创建PrintWriter对象，允许追加写入
            PrintWriter fw = new PrintWriter(new BufferedWriter(new FileWriter(fileFullPath, true)));
            logger.info("保存数据到文件---{}", fileFullPath);
            // 格式化时间为字符串
            String timeStr = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(new Date(message.getInitialTime()));
            // 写入时间标记和消息内容
            fw.println("@&" + timeStr + "&@");
            fw.println(message.getMsg());
            // 刷新并关闭文件
            fw.flush();
            fw.close();
        } catch (Exception e) {
            System.out.println();
            logger.error("写入文件时报错！", e);
        }
    }
}
