//package com.snct.kafka.listener;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import com.snct.common.core.domain.entity.SysDept;
//import com.snct.common.core.redis.RedisCache;
//import com.snct.hbase.service.StoreService;
//import com.snct.kafka.KafkaMessage;
//import com.snct.kafka.handler.AbstractDeviceHandler;
//import com.snct.kafka.handler.DeviceHandlerFactory;
//import com.snct.system.domain.Device;
//import com.snct.system.domain.Ship;
//import com.snct.system.service.IDeviceService;
//import com.snct.system.service.IShipService;
//import com.snct.system.service.ISysDeptService;
//import org.apache.kafka.clients.consumer.ConsumerRecord;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.kafka.annotation.KafkaListener;
//import org.springframework.kafka.core.KafkaTemplate;
//import org.springframework.stereotype.Component;
//import org.springframework.util.StringUtils;
//
//import java.util.List;
//import java.util.Optional;
//
///**
// * Kafka消息消费者服务
// * 处理各类设备数据和船舶信息
// *
// * <AUTHOR>
// */
//@Component
//public class ConsumerService {
//
//    public final static Logger logger = LoggerFactory.getLogger(ConsumerService.class);
//
//    @Autowired
//    private DeviceHandlerFactory deviceHandlerFactory;
//
//    /**
//     * 接收Kafka消息并处理
//     *
//     * @param record Kafka消息记录
//     */
//    @KafkaListener(topics = {"snct_device_source_data_topic"}, groupId = "dola_unified")
//    public void deviceConsumer(ConsumerRecord<?, ?> record) {
//        Optional<?> sourceMessage = Optional.ofNullable(record.value());
//        if (!sourceMessage.isPresent()) {
//            return;
//        }
//
//        Object message = sourceMessage.get();
//        logger.info("---接收到数据----{}", message);
//
//        try {
//            KafkaMessage kafkaMessage = JSON.parseObject(message.toString(), KafkaMessage.class);
//            if (StringUtils.isEmpty(kafkaMessage.getMsg().trim())) {
//                return;
//            }
//
//            // 提取消息基本信息
//            String sn = kafkaMessage.getSn();
//            Integer type = kafkaMessage.getType();
//            String code = kafkaMessage.getCode();
//            String msgContent = kafkaMessage.getMsg();
//
//            // 设置初始时间（如果为null）
//            if (kafkaMessage.getInitialTime() == null) {
//                kafkaMessage.setInitialTime(System.currentTimeMillis());
//            }
//
//            // 根据消息类型和代码获取对应的处理器
//            try {
//                AbstractDeviceHandler handler = deviceHandlerFactory.getHandler(type, code);
//                handler.processDeviceData(kafkaMessage);
//            } catch (IllegalArgumentException e) {
//                logger.warn("不支持的消息类型: {}, 消息代码: {}, 错误: {}", type, code, e.getMessage());
//            }
//        } catch (Exception e) {
//            logger.error("处理Kafka消息异常: {}", e.getMessage(), e);
//        }
//    }
//}