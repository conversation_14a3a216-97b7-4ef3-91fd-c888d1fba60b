package com.snct.utils;

/**
 * 十六进制工具类
 * 用于处理十六进制字符串和字节数组之间的转换
 */
public class HexUtil {


    /**
     * 十六进制字符串转换为普通字符串
     */
    public static String hexStringToString(String hexString) {
        if (hexString == null || hexString.length() % 2 != 0) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < hexString.length(); i += 2) {
            String hex = hexString.substring(i, i + 2);
            int decimal = Integer.parseInt(hex, 16);
            sb.append((char) decimal);
        }
        return sb.toString();
    }

    /**
     * 字节数组转十六进制字符串
     * @param bytes 字节数组
     * @return 十六进制字符串
     */
    public static String byteArrayToHex(byte[] bytes) {
        if (bytes == null || bytes.length == 0) {
            return "";
        }
        
        StringBuilder sb = new StringBuilder(bytes.length * 2);
        for (byte b : bytes) {
            sb.append(String.format("%02X", b & 0xff));
        }
        
        return sb.toString();
    }

    /**
     * 将字节数组转换为16进制字符串
     *
     * @param src 字节数组
     * @return 转换后的16进制字符串(大写)
     */
    public static String bytesTo16String(byte[] src) {
        StringBuilder stringBuilder = new StringBuilder("");
        if (src == null || src.length <= 0) {
            return null;
        }
        for (byte b : src) {
            int v = b & 255;
            String hv = Integer.toHexString(v);
            if (hv.length() < 2) {
                stringBuilder.append(0);
            }
            stringBuilder.append(hv);
        }
        return stringBuilder.toString().toUpperCase();
    }

    /**
     * 格式化十六进制字符串，每两个字符之间添加空格
     *
     * @param hexString 原始十六进制字符串
     * @return 格式化后的字符串
     */
    public static String formatHexString(String hexString) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < hexString.length(); i += 2) {
            if (i + 2 <= hexString.length()) {
                sb.append(hexString.substring(i, i + 2)).append(" ");
            } else {
                sb.append(hexString.substring(i));
            }
        }
        return sb.toString().trim();
    }
    
    /**
     * 十六进制字符串转字节数组
     * @param hexString 十六进制字符串
     * @return 字节数组
     */
    public static byte[] hexToByteArray(String hexString) {
        if (hexString == null || hexString.isEmpty()) {
            return new byte[0];
        }
        
        // 确保十六进制字符串长度为偶数
        if (hexString.length() % 2 != 0) {
            hexString = "0" + hexString;
        }
        
        int len = hexString.length();
        byte[] bytes = new byte[len / 2];
        
        for (int i = 0; i < len; i += 2) {
            // 从十六进制字符串中取出两个字符
            String subStr = hexString.substring(i, i + 2);
            // 将十六进制字符串转换为字节
            bytes[i / 2] = (byte) Integer.parseInt(subStr, 16);
        }
        
        return bytes;
    }

    /**
     * 将十六进制字符串转换为整数
     */
    public static Integer hexToInt(String hexStr) {
        return Integer.valueOf(hexStr, 16);
    }

    /**
     * 将十六进制字符串转换为浮点数，用于解析需要小数点的值
     * 例如: "0936" -> 23.58 (电压值)
     * @param hexStr 十六进制字符串
     * @param divisor 除数，用于将整数转换为小数
     * @return 转换后的浮点数
     */
    public static double hexToDouble(String hexStr, double divisor) {
        int value = hexToInt(hexStr);
        return value / divisor;
    }
    
    /**
     * 计算校验和
     * 简单的校验和算法：将所有字节相加，取低8位
     * @param bytes 字节数组
     * @return 校验和的十六进制字符串
     */
    public static String calculateChecksum(byte[] bytes) {
        if (bytes == null || bytes.length == 0) {
            return "00";
        }
        
        int sum = 0;
        for (byte b : bytes) {
            sum += (b & 0xff);
        }
        
        // 取低8位
        sum = sum & 0xff;
        
        return String.format("%02X", sum);
    }
    
    /**
     * 计算校验和
     * @param hexString 十六进制字符串
     * @return 校验和的十六进制字符串
     */
    public static String calculateChecksum(String hexString) {
        if (hexString == null || hexString.isEmpty()) {
            return "00";
        }

        byte[] bytes = hexToByteArray(hexString);
        return calculateChecksum(bytes);
    }

    /**
     * 将字符串转换为GB2312编码的十六进制字符串
     * 用于北斗内容混编（编码类别3）
     *
     * @param text 要编码的文本内容
     * @return GB2312编码的十六进制字符串，以"A4"开头
     */
    public static String stringToGB2312Hex(String text) {
        if (text == null || text.isEmpty()) {
            return "A4";
        }

        try {
            // 使用GB2312编码将字符串转换为字节数组
            byte[] gb2312Bytes = text.getBytes("GB2312");

            // 转换为十六进制字符串
            String hexString = byteArrayToHex(gb2312Bytes);

            // BCD码起始位"A4" + 编码后的内容
            return "A4" + hexString;

        } catch (Exception e) {
            // 如果编码失败，返回起始位
            return "A4";
        }
    }

    /**
     * 将GB2312编码的十六进制字符串转换回文本
     * 用于调试和验证
     *
     * @param gb2312Hex GB2312编码的十六进制字符串（以A4开头）
     * @return 解码后的文本内容
     */
    public static String gb2312HexToString(String gb2312Hex) {
        if (gb2312Hex == null || gb2312Hex.length() < 2) {
            return "";
        }

        try {
            // 去掉起始位"A4"
            String hexContent = gb2312Hex.startsWith("A4") ? gb2312Hex.substring(2) : gb2312Hex;

            if (hexContent.isEmpty()) {
                return "";
            }

            // 将十六进制字符串转换为字节数组
            byte[] bytes = hexToByteArray(hexContent);

            // 使用GB2312解码
            return new String(bytes, "GB2312");

        } catch (Exception e) {
            return "";
        }
    }

    /**
     * 计算NMEA格式消息的XOR校验和
     * 用于NMEA协议消息校验，跳过起始符号"$"进行计算
     *
     * @param nmeaMessage NMEA格式消息字符串（包含$符号）
     * @return XOR校验和的十六进制字符串（大写，补零到2位）
     */
    public static String calculateNmeaXorChecksum(String nmeaMessage) {
        if (nmeaMessage == null || nmeaMessage.length() <= 1) {
            return "00";
        }

        // 跳过第一个字符"$"，从第二个字符开始计算
        char[] chars = nmeaMessage.substring(1).toCharArray();
        int xorResult = 0;

        for (char c : chars) {
            xorResult ^= c;
        }

        String hex = Integer.toHexString(xorResult).toUpperCase();
        return hex.length() < 2 ? "0" + hex : hex;
    }

    /**
     * 计算字符串的XOR校验和
     * 用于北斗通信协议等需要XOR校验的场景
     *
     * @param data 需要计算校验和的字符串
     * @return XOR校验和的十六进制字符串（大写，补零到2位）
     */
    public static String calculateXorChecksum(String data) {
        if (data == null || data.isEmpty()) {
            return "00";
        }

        int xorResult = 0;
        for (char c : data.toCharArray()) {
            xorResult ^= c;
        }

        String hex = Integer.toHexString(xorResult).toUpperCase();
        return hex.length() < 2 ? "0" + hex : hex;
    }

}