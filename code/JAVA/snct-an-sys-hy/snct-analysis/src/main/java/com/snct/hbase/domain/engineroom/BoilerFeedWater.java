package com.snct.hbase.domain.engineroom;

import com.snct.hbase.utils.AnalysisUtils;

import java.util.Map;

/**
 * 锅炉给水系统
 * <AUTHOR>
 */
public class BoilerFeedWater {

    /**
     * 锅炉最低流压  10013
     */
    private String mfPressure;

    /**
     * 锅炉最低回水温度  10014
     */
    private String mfrTemp;

    /**
     * 锅炉出口温度  10008M
     */
    private String outletTemp;

    /**
     * 锅炉回水温度  10009
     */
    private String frTemp;

    /**
     * 数据时间
     */
    private Long timeStamp;

    public String getMfPressure() {
        return mfPressure;
    }

    public void setMfPressure(String mfPressure) {
        this.mfPressure = mfPressure;
    }

    public String getMfrTemp() {
        return mfrTemp;
    }

    public void setMfrTemp(String mfrTemp) {
        this.mfrTemp = mfrTemp;
    }

    public String getOutletTemp() {
        return outletTemp;
    }

    public void setOutletTemp(String outletTemp) {
        this.outletTemp = outletTemp;
    }

    public String getFrTemp() {
        return frTemp;
    }

    public void setFrTemp(String frTemp) {
        this.frTemp = frTemp;
    }

    public Long getTimeStamp() {
        return timeStamp;
    }

    public void setTimeStamp(Long timeStamp) {
        this.timeStamp = timeStamp;
    }

    public BoilerFeedWater(){

    }

    public BoilerFeedWater(Map<String,EngineroomData> map){
        this.mfPressure = AnalysisUtils.analysis(map.get("10013"));
        this.mfrTemp = AnalysisUtils.analysis(map.get("10014"));
        this.outletTemp = AnalysisUtils.analysis(map.get("10008"));
        this.frTemp = AnalysisUtils.analysis(map.get("10009")) ;

    }

    public BoilerFeedWater(String msg){

        String[] strbuff = msg.split(",",5);
        this.mfPressure = strbuff[0];
        this.mfrTemp =strbuff[1];
        this.outletTemp =strbuff[2];
        this.frTemp =strbuff[3];
        this.timeStamp = Long.parseLong(strbuff[4]);
    }

    @Override
    public String toString() {
        return "BoilerFeedWater{" +
                "mfPressure='" + mfPressure + '\'' +
                ", mfrTemp='" + mfrTemp + '\'' +
                ", outletTemp='" + outletTemp + '\'' +
                ", frTemp='" + frTemp + '\'' +
                ", timeStamp=" + timeStamp +
                '}';
    }

    public String mergeSendStr() {
        StringBuffer sb = new StringBuffer();
        sb.append(mfPressure == null ? "" : mfPressure).append(",");
        sb.append(mfrTemp == null ? "" : mfrTemp).append(",");
        sb.append(outletTemp == null ? "" : outletTemp).append(",");
        sb.append(frTemp == null ? "" : frTemp).append(",");
        sb.append(timeStamp == null ? "" : timeStamp);
        return sb.toString();
    }
}
