//package com.snct.web.controller.business;
//
//import com.snct.common.annotation.Log;
//import com.snct.common.core.controller.BaseController;
//import com.snct.common.core.domain.AjaxResult;
//import com.snct.common.core.page.TableDataInfo;
//import com.snct.common.enums.BusinessType;
//import com.snct.common.utils.poi.ExcelUtil;
//import com.snct.system.domain.DataStatistics;
//import com.snct.system.service.IDataStatisticsService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.security.access.prepost.PreAuthorize;
//import org.springframework.web.bind.annotation.*;
//
//import javax.servlet.http.HttpServletResponse;
//import java.util.List;
//
///**
// * 采集-传输记录Controller
// *
// * <AUTHOR>
// * @date 2025-04-09
// */
//@RestController
//@RequestMapping("/business/dataStatistics")
//public class DataStatisticsController extends BaseController
//{
//    @Autowired
//    private IDataStatisticsService dataStatisticsService;
//
//    /**
//     * 查询采集-传输记录列表
//     */
//    @PreAuthorize("@ss.hasPermi('business:dataStatistics:list')")
//    @GetMapping("/list")
//    public TableDataInfo list(DataStatistics dataStatistics)
//    {
//        startPage();
//        List<DataStatistics> list = dataStatisticsService.selectDataStatisticsList(dataStatistics);
//        return getDataTable(list);
//    }
//
//    /**
//     * 导出采集-传输记录列表
//     */
//    @PreAuthorize("@ss.hasPermi('business:dataStatistics:export')")
//    @Log(title = "采集-传输记录", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, DataStatistics dataStatistics)
//    {
//        List<DataStatistics> list = dataStatisticsService.selectDataStatisticsList(dataStatistics);
//        ExcelUtil<DataStatistics> util = new ExcelUtil<DataStatistics>(DataStatistics.class);
//        util.exportExcel(response, list, "采集-传输记录数据");
//    }
//
//    /**
//     * 获取采集-传输记录详细信息
//     */
//    @PreAuthorize("@ss.hasPermi('business:dataStatistics:query')")
//    @GetMapping(value = "/{id}")
//    public AjaxResult getInfo(@PathVariable("id") Long id)
//    {
//        return success(dataStatisticsService.selectDataStatisticsById(id));
//    }
//
//    /**
//     * 新增采集-传输记录
//     */
//    @PreAuthorize("@ss.hasPermi('business:dataStatistics:add')")
//    @Log(title = "采集-传输记录", businessType = BusinessType.INSERT)
//    @PostMapping
//    public AjaxResult add(@RequestBody DataStatistics dataStatistics)
//    {
//        return toAjax(dataStatisticsService.insertDataStatistics(dataStatistics));
//    }
//
//    /**
//     * 修改采集-传输记录
//     */
//    @PreAuthorize("@ss.hasPermi('business:dataStatistics:edit')")
//    @Log(title = "采集-传输记录", businessType = BusinessType.UPDATE)
//    @PutMapping
//    public AjaxResult edit(@RequestBody DataStatistics dataStatistics)
//    {
//        return toAjax(dataStatisticsService.updateDataStatistics(dataStatistics));
//    }
//
//    /**
//     * 删除采集-传输记录
//     */
//    @PreAuthorize("@ss.hasPermi('business:dataStatistics:remove')")
//    @Log(title = "采集-传输记录", businessType = BusinessType.DELETE)
//	@DeleteMapping("/{ids}")
//    public AjaxResult remove(@PathVariable Long[] ids)
//    {
//        return toAjax(dataStatisticsService.deleteDataStatisticsByIds(ids));
//    }
//}
