//package com.snct.web.controller.business.hbase;
//
//import com.snct.common.core.controller.BaseController;
//import com.snct.common.core.domain.AjaxResult;
//import com.snct.common.utils.SecurityUtils;
//import com.snct.hbase.domain.hbase.ModemHbaseVo;
//import com.snct.hbase.service.HBaseDeviceDataService;
//import com.snct.system.mapper.SysDeptMapper;
//import com.snct.common.core.domain.entity.SysDept;
//import com.snct.system.domain.msg.BuMsgModem;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import io.swagger.annotations.ApiParam;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.security.access.prepost.PreAuthorize;
//import org.springframework.web.bind.annotation.*;
//
//import java.text.SimpleDateFormat;
//import java.util.ArrayList;
//import java.util.Date;
//import java.util.List;
//import java.util.Map;
//import java.util.stream.Collectors;
//
///**
// * Modem消息Controller（HBase版本）
// *
// * <AUTHOR>
// * @date 2025-04-24
// */
//@Api("Modem消息(hbase)")
//@RestController
//@RequestMapping("/hbase/devicemsg/modem")
//public class BuMsgModemHbaseController extends BaseController
//{
//    /**
//     * 设备类型常量
//     */
//    private static final String DEVICE_TYPE = "modem";
//
//    @Autowired
//    private HBaseDeviceDataService deviceDataService;
//
//    @Autowired
//    private SysDeptMapper deptMapper;
//
//    /**
//     * 基于游标的分页查询接口
//     */
//    @ApiOperation("查询Modem消息列表")
//    @PreAuthorize("@ss.hasPermi('devicemsg:modem:list')")
//    @GetMapping("/query")
//    public AjaxResult query(
//            @ApiParam("部门ID") @RequestParam(required = false) Long deptId,
//            @ApiParam("船舶SN") @RequestParam(required = false) String sn,
//            @ApiParam("设备ID") @RequestParam(required = false) Long deviceId,
//            @ApiParam("开始日期时间(yyyy-MM-dd HH:mm:ss)") @RequestParam(required = false) String startDateTime,
//            @ApiParam("结束日期时间(yyyy-MM-dd HH:mm:ss)") @RequestParam(required = false) String endDateTime,
//            @ApiParam("每页大小") @RequestParam(defaultValue = "20") Integer limit,
//            @ApiParam("游标(上一页最后一条记录的RowKey)") @RequestParam(required = false) String cursor) {
//
//        // 获取当前用户信息
//        Long currentUserDeptId = getDeptId();
//        Long currentUserId = SecurityUtils.getUserId();
//        boolean isAdmin = SecurityUtils.isAdmin(currentUserId);
//
//        // 非管理员只能查看自己部门及子部门的数据
//        Long effectiveDeptId = deptId;
//        if (!isAdmin) {
//            // 如果不是管理员，且指定了查询部门ID
//            if (deptId != null && !deptId.equals(currentUserDeptId)) {
//                // 获取当前用户部门的所有子部门
//                List<SysDept> childDepts = deptMapper.selectChildrenDeptById(currentUserDeptId);
//                List<Long> childDeptIds = childDepts.stream().map(SysDept::getDeptId).collect(Collectors.toList());
//
//                // 检查指定的部门ID是否在子部门列表中
//                if (!childDeptIds.contains(deptId)) {
//                    effectiveDeptId = currentUserDeptId;
//                    logger.warn("用户无权查询部门ID: {}, 已限制为当前用户部门ID: {}", deptId, currentUserDeptId);
//                }
//            } else {
//                // 如果未指定部门ID或指定的就是当前用户部门，则使用当前用户部门ID
//                effectiveDeptId = currentUserDeptId;
//            }
//        }
//
//        // 参数日志
//        logger.info("游标分页查询参数: deptId={}, sn={}, deviceId={}, 时间范围={}-{}, limit={}, cursor={}",
//                effectiveDeptId, sn, deviceId, startDateTime, endDateTime, limit, cursor);
//
//        // 查询数据
//        Map<String, Object> queryResult = deviceDataService.queryCursorPagination(
//                DEVICE_TYPE, effectiveDeptId, sn, deviceId,
//                startDateTime, endDateTime, limit, cursor, isAdmin, ModemHbaseVo.class);
//
//        return AjaxResult.success(queryResult);
//    }
//
//    /**
//     * 获取详情接口
//     */
//    @ApiOperation("获取Modem消息详情")
//    @PreAuthorize("@ss.hasPermi('devicemsg:modem:query')")
//    @GetMapping("/detail/{rowKey}")
//    public AjaxResult getDetail(@PathVariable("rowKey") String rowKey) {
//        ModemHbaseVo detail = deviceDataService.getDeviceDetail(DEVICE_TYPE, rowKey, ModemHbaseVo.class);
//        if (detail == null) {
//            return AjaxResult.error("未找到指定的数据记录");
//        }
//        return AjaxResult.success(detail);
//    }
//
//    /**
//     * 将HBase实体转换为前端接口实体
//     */
//    private List<BuMsgModem> convertToBuMsgModem(List<ModemHbaseVo> hbaseVoList) {
//        List<BuMsgModem> result = new ArrayList<>();
//
//        if (hbaseVoList == null || hbaseVoList.isEmpty()) {
//            return result;
//        }
//
//        for (ModemHbaseVo vo : hbaseVoList) {
//            BuMsgModem modem = convertToBuMsgModem(vo);
//            if (modem != null) {
//                result.add(modem);
//            }
//        }
//
//        return result;
//    }
//
//    /**
//     * 将单个HBase实体转换为前端接口实体
//     */
//    private BuMsgModem convertToBuMsgModem(ModemHbaseVo vo) {
//        if (vo == null) {
//            return null;
//        }
//
//        BuMsgModem modem = new BuMsgModem();
//
//        try {
//            modem.setId(0L);
//            modem.setDeptId(0L);
//            modem.setShipId(0L);
//            modem.setDeviceId(0L);
//            modem.setIsFlag(0L);
//            modem.setStatus(0L);
//
//            if (vo.getId() != null) {
//                try {
//                    // 尝试将整个rowKey直接用作id
//                    modem.setId(Long.parseLong(vo.getId()));
//                } catch (NumberFormatException e) {
//                    // 如果rowKey不是数字格式，则使用默认值0L
//                    modem.setId(0L);
//                    logger.debug("rowKey不是有效的数字格式: {}", vo.getId());
//                }
//            }
//
//            if (vo.getDeptId() != null) {
//                modem.setDeptId(vo.getDeptId());
//            }
//
//            if (vo.getShipId() != null) {
//                modem.setShipId(vo.getShipId());
//            }
//
//            if (vo.getDeviceId() != null) {
//                modem.setDeviceId(vo.getDeviceId());
//            }
//
//            // 设置业务数据，增加空值检查
//            if (vo.getSignal() != null) {
//                modem.setSignal(vo.getSignal());
//            }
//
//            if (vo.getSpeed() != null) {
//                modem.setSpeed(vo.getSpeed());
//            }
//
//            if (vo.getSendPower() != null) {
//                modem.setSendPower(vo.getSendPower());
//            }
//
//            if (vo.getIsFlag() != null) {
//                modem.setIsFlag(vo.getIsFlag());
//            }
//
//            if (vo.getStatus() != null) {
//                modem.setStatus(vo.getStatus());
//            }
//
//            // 设置创建时间（从HBase的initialBjTime转换）
//            if (vo.getInitialBjTime() != null && !vo.getInitialBjTime().isEmpty()) {
//                try {
//                    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//                    Date createTime = dateFormat.parse(vo.getInitialBjTime());
//                    modem.setCreateTime(createTime);
//                } catch (Exception e) {
//                    // 时间解析错误，使用当前时间
//                    modem.setCreateTime(new Date());
//                }
//            } else {
//                // 如果没有时间字段，使用当前时间
//                modem.setCreateTime(new Date());
//            }
//
//        } catch (Exception e) {
//            logger.error("数据转换错误: {}", e.getMessage(), e);
//            return modem;
//        }
//
//        return modem;
//    }
//}
