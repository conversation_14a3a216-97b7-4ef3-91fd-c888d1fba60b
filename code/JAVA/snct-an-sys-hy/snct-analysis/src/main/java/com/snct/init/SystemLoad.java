package com.snct.init;

import com.snct.common.utils.spring.SpringUtils;
import com.snct.serialport.RtxtService;
import com.snct.service.SendService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Component
@Order(0)
public class SystemLoad implements CommandLineRunner {
    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Override
    public void run(String... args) throws Exception {
        logger.info("系统基础功能初始化开始...");

        //串口监听
        RtxtService inputService = SpringUtils.getBean(RtxtService.class);
        inputService.handleAllService();

        //数据转发
        SendService sendService = SpringUtils.getBean(SendService.class);
        sendService.cleanSendParametersMap();
        sendService.renewLaputaIpPort();
        sendService.consumeRedisList();

        logger.info("系统基础功能初始化结束...");
    }
}
