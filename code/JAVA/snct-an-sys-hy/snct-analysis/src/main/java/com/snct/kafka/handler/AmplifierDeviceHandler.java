package com.snct.kafka.handler;

import com.alibaba.fastjson.JSONObject;
import com.snct.kafka.KafkaMessage;
import com.snct.system.domain.msg.BuMsgAmplifier;
import com.snct.system.service.IBuMsgAmplifierService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 功放设备数据处理器
 *
 * <AUTHOR>
 */
@Component
public class AmplifierDeviceHandler extends AbstractDeviceHandler {

    @Autowired
    private IBuMsgAmplifierService buMsgAmplifierService;

    @Override
    protected boolean saveToMysql(JSONObject jsonObject, String sn, Map<String, Object> deviceInfo) {
        try {
            // 创建MySQL实体对象
            BuMsgAmplifier amplifier = new BuMsgAmplifier();
            
            // 设置基本字段
            Long deviceId = jsonObject.getLong("deviceId");
            amplifier.setDeptId((Long)deviceInfo.get("deptId"));
            amplifier.setShipId((Long)deviceInfo.get("shipId"));
            amplifier.setDeviceId(deviceId);
            
            // 设置设备特定字段
            amplifier.setDecay(jsonObject.getDouble("decay"));
            amplifier.setTemp(jsonObject.getDouble("temp"));
            amplifier.setOutPower(jsonObject.getDouble("outPower"));
            amplifier.setBucStatus(jsonObject.getLong("bucStatus"));
            amplifier.setStatus(0L); // 默认状态

            // 保存到MySQL
            buMsgAmplifierService.insertBuMsgAmplifier(amplifier);
            
            logger.info("功放数据已保存到MySQL，DeviceId: {}", deviceId);
            return true;
        } catch (Exception e) {
            logger.error("保存功放数据到MySQL失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    protected boolean saveToHbase(KafkaMessage kafkaMessage) {
        try {
            // 保存到HBase
            storeService.save2Hbase(kafkaMessage);
            logger.info("功放数据已保存到HBase");
            return true;
        } catch (Exception e) {
            logger.error("保存功放数据到HBase失败: {}", e.getMessage(), e);
            return false;
        }
    }
} 