package com.snct.hbase.domain.device;


import com.snct.hbase.domain.HbaseBaseEntity;

/**
 * class
 *
 * <AUTHOR>
 */
public class DeviceAttributeEntityHbase extends HbaseBaseEntity {
    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 设备类型
     */
    private Integer type;
    /**
     * 名称
     */
    private String name;

    /**
     * 标签
     */
    private String label;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

}
