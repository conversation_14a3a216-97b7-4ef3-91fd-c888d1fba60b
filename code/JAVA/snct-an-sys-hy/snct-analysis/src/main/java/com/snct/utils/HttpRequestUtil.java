package com.snct.utils;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

/**
 * HTTP 工具类
 */
@Component
public class HttpRequestUtil {

    private static final Logger logger = LoggerFactory.getLogger(HttpRequestUtil.class);

    private final RestTemplate restTemplate;

    public HttpRequestUtil(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    /**
     * 发送 GET 请求
     */
    public String sendGet(String url, Map<String, Object> params, Map<String, String> headers) {
        try {
            String fullUrl = buildUrlWithParams(url, params);
            logger.info("发送 GET 请求: {}", fullUrl);

            HttpHeaders httpHeaders = buildHeaders(headers);
            HttpEntity<String> entity = new HttpEntity<>(httpHeaders);

            ResponseEntity<String> response = restTemplate.exchange(fullUrl, HttpMethod.GET, entity, String.class);
            return response.getBody();
        } catch (Exception e) {
            logger.error("GET 请求异常：url={}, params={}", url, params, e);
            return null;
        }
    }

    /**
     * 发送 POST 请求 (JSON)
     */
    public String sendPost(String url, Map<String, Object> params, Map<String, String> headers) {
        try {
            logger.info("发送 POST 请求: {}, 数据: {}", url, params);

            HttpHeaders httpHeaders = buildHeaders(headers);
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(params, httpHeaders);
            ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);

            return response.getBody();
        } catch (Exception e) {
            logger.error("POST 请求异常：url={}, params={}", url, params, e);
            return null;
        }
    }

    /**
     * 发送 POST 表单请求
     */
    public String sendPostForm(String url, Map<String, Object> params, Map<String, String> headers) {
        try {
            logger.info("发送 POST 表单请求: {}, 数据: {}", url, params);

            HttpHeaders httpHeaders = buildHeaders(headers);
            httpHeaders.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

            HttpEntity<String> entity = new HttpEntity<>(buildFormData(params), httpHeaders);
            return restTemplate.postForObject(url, entity, String.class);
        } catch (Exception e) {
            logger.error("POST 表单请求异常：url={}, params={}", url, params, e);
            return null;
        }
    }

    /**
     * 构建 URL 参数
     */
    private String buildUrlWithParams(String url, Map<String, Object> params) {
        if (params == null || params.isEmpty()) return url;

        StringBuilder sb = new StringBuilder(url);
        sb.append(url.contains("?") ? "&" : "?");

        params.forEach((k, v) -> {
            sb.append(k).append("=").append(v).append("&");
        });

        return sb.substring(0, sb.length() - 1); // 移除最后一个 &
    }

    /**
     * 构建请求头
     */
    private HttpHeaders buildHeaders(Map<String, String> headers) {
        HttpHeaders httpHeaders = new HttpHeaders();
        if (headers != null) {
            headers.forEach(httpHeaders::set);
        }
        return httpHeaders;
    }

    /**
     * 构建表单格式参数
     */
    private String buildFormData(Map<String, Object> params) {
        if (params == null || params.isEmpty()) return "";

        StringBuilder sb = new StringBuilder();
        params.forEach((k, v) -> {
            sb.append(k).append("=").append(v).append("&");
        });

        return sb.substring(0, sb.length() - 1);
    }

    /**
     * 快捷 JSON 解析
     */
    public static JSONObject parseJson(String json) {
        if (StringUtils.isBlank(json)) return null;

        try {
            return JSONObject.parseObject(json);
        } catch (Exception e) {
            logger.error("JSON 解析失败: {}", json, e);
            return null;
        }
    }
}
