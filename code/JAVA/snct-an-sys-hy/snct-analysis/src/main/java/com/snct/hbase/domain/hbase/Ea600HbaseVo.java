package com.snct.hbase.domain.hbase;


import com.snct.hbase.annotation.Excel;
import com.snct.hbase.annotation.HBaseColumn;
import com.snct.hbase.annotation.HBaseTable;

/**
 * @description:  EA600数据 1秒一组
 * @author: rr
 * @create: 2020-06-03 10:34
 **/
@HBaseTable(tableName = "ns1:ea600-15")
public class Ea600HbaseVo {

    @HBaseColumn(family = "rowkey", qualifier = "rowkey")
    private String id;

    /**
     * 部门id
     */
    @HBaseColumn(family = "i", qualifier = "d_p")
    private String deptId;

    /** sn */
    @HBaseColumn(family = "i", qualifier = "s_n")
    private String sn;
    /**
     * 录入时间
     */
    @HBaseColumn(family = "i", qualifier = "i_t")
    private String initialTime;
    /**
     * 录入时间(北京时间yyyy-MM-dd HH:mm:ss)
     */
    @Excel(name="录入时间")
    @HBaseColumn(family = "i", qualifier = "i_b_t")
    private String initialBjTime;

    /**
     * water depth (in feet) 水深
     */
    @Excel(name="水深")
    @HBaseColumn(family = "i", qualifier = "w_d_f")
    private String waterDepthF;
    /**
     * 2.水深单位 英尺
     */
    @Excel(name="单位 英尺")
    @HBaseColumn(family = "i", qualifier = "wuf")
    private String waterUnitF;
    /**
     * water depth (in meters) 水深
     */
    @Excel(name="水深")
    @HBaseColumn(family = "i", qualifier = "w_d_m")
    private String waterDepthM;
    /**
     * 4.水深单位 米
     */
    @Excel(name="单位 米")
    @HBaseColumn(family = "i", qualifier = "wum")
    private String waterUnitM;
    /**
     * 5.water depth (in fathoms)
     */
    @HBaseColumn(family = "i", qualifier = "wdi")
    private String waterDepthI;

    /**
     * 纬度
     */
    @HBaseColumn(family = "i", qualifier = "i_l_a")
    private String latitude;
    /**
     * 经度
     */
    @HBaseColumn(family = "i", qualifier = "i_l_o")
    private String longitude;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public void setDeptId(String deptId)
    {
        this.deptId = deptId;
    }

    public String getDeptId()
    {
        return deptId;
    }

    public void setSn(String sn)
    {
        this.sn = sn;
    }

    public String getSn()
    {
        return sn;
    }

    public String getInitialTime() {
        return initialTime;
    }

    public void setInitialTime(String initialTime) {
        this.initialTime = initialTime;
    }

    public String getInitialBjTime() {
        return initialBjTime;
    }

    public void setInitialBjTime(String initialBjTime) {
        this.initialBjTime = initialBjTime;
    }

    public String getWaterDepthF() {
        return waterDepthF;
    }

    public void setWaterDepthF(String waterDepthF) {
        this.waterDepthF = waterDepthF;
    }

    public String getWaterUnitF() {
        return waterUnitF;
    }

    public void setWaterUnitF(String waterUnitF) {
        this.waterUnitF = waterUnitF;
    }

    public String getWaterDepthM() {
        return waterDepthM;
    }

    public void setWaterDepthM(String waterDepthM) {
        this.waterDepthM = waterDepthM;
    }

    public String getWaterUnitM() {
        return waterUnitM;
    }

    public void setWaterUnitM(String waterUnitM) {
        this.waterUnitM = waterUnitM;
    }

    public String getWaterDepthI() {
        return waterDepthI;
    }

    public void setWaterDepthI(String waterDepthI) {
        this.waterDepthI = waterDepthI;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }
}
