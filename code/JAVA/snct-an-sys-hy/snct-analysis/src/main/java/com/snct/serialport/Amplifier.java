package com.snct.serialport;

public class Amplifier{
    private String decay;
    private String temp;
    private String outPower;
    private String bucStatus;
    private String utcTime;

    public String getUtcTime() {
        return this.utcTime;
    }

    public void setUtcTime(String utcTime) {
        this.utcTime = utcTime;
    }

    public String getDecay() {
        return this.decay;
    }

    public void setDecay(String decay) {
        this.decay = decay;
    }

    public String getTemp() {
        return this.temp;
    }

    public void setTemp(String temp) {
        this.temp = temp;
    }

    public String getOutPower() {
        return this.outPower;
    }

    public void setOutPower(String outPower) {
        this.outPower = outPower;
    }

    public String getBucStatus() {
        return this.bucStatus;
    }

    public void setBucStatus(String bucStatus) {
        this.bucStatus = bucStatus;
    }

    @Override
    public String toString() {
        return "Amplifier{" +
                "decay='" + decay + '\'' +
                ", temp='" + temp + '\'' +
                ", outPower='" + outPower + '\'' +
                ", bucStatus='" + bucStatus + '\'' +
                ", utcTime='" + utcTime + '\'' +
                '}';
    }
}