//package com.snct.web.controller.business;
//
//import com.snct.common.annotation.Log;
//import com.snct.common.core.controller.BaseController;
//import com.snct.common.core.domain.AjaxResult;
//import com.snct.common.core.page.TableDataInfo;
//import com.snct.common.enums.BusinessType;
//import com.snct.common.utils.poi.ExcelUtil;
//import com.snct.system.domain.msg.BuMsgPduOut;
//import com.snct.system.service.IBuMsgPduOutService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.security.access.prepost.PreAuthorize;
//import org.springframework.web.bind.annotation.*;
//
//import javax.servlet.http.HttpServletResponse;
//import java.util.List;
//
///**
// * pdu-out消息Controller
// *
// * <AUTHOR>
// * @date 2025-04-24
// */
//@RestController
//@RequestMapping("/devicemsg/out")
//public class BuMsgPduOutController extends BaseController
//{
//    @Autowired
//    private IBuMsgPduOutService buMsgPduOutService;
//
//    /**
//     * 查询pdu-out消息列表
//     */
//    @PreAuthorize("@ss.hasPermi('devicemsg:out:list')")
//    @GetMapping("/list")
//    public TableDataInfo list(BuMsgPduOut buMsgPduOut)
//    {
//        startPage();
//        List<BuMsgPduOut> list = buMsgPduOutService.selectBuMsgPduOutList(buMsgPduOut);
//        return getDataTable(list);
//    }
//
//    /**
//     * 通过PDU ID查询同一批次的PDU_OUT记录
//     */
//    @PreAuthorize("@ss.hasPermi('devicemsg:pduout:list')")
//    @GetMapping("/listByPduId/{pduId}")
//    public TableDataInfo listByPduId(@PathVariable("pduId") Long pduId)
//    {
//        startPage();
//        String batchCode = pduId.toString();
//
//        // 创建查询条件
//        BuMsgPduOut queryParam = new BuMsgPduOut();
//        queryParam.setBatchCode(batchCode);
//
//        // 查询数据
//        List<BuMsgPduOut> list = buMsgPduOutService.selectBuMsgPduOutList(queryParam);
//
//        return getDataTable(list);
//    }
//
//    /**
//     * 获取pdu-out消息详细信息
//     */
//    @PreAuthorize("@ss.hasPermi('devicemsg:out:query')")
//    @GetMapping(value = "/{id}")
//    public AjaxResult getInfo(@PathVariable("id") Long id)
//    {
//        return success(buMsgPduOutService.selectBuMsgPduOutById(id));
//    }
//
//    /**
//     * 新增pdu-out消息
//     */
//    @PreAuthorize("@ss.hasPermi('devicemsg:out:add')")
//    @Log(title = "pdu-out消息", businessType = BusinessType.INSERT)
//    @PostMapping
//    public AjaxResult add(@RequestBody BuMsgPduOut buMsgPduOut)
//    {
//        return toAjax(buMsgPduOutService.insertBuMsgPduOut(buMsgPduOut));
//    }
//
//    /**
//     * 修改pdu-out消息
//     */
//    @PreAuthorize("@ss.hasPermi('devicemsg:out:edit')")
//    @Log(title = "pdu-out消息", businessType = BusinessType.UPDATE)
//    @PutMapping
//    public AjaxResult edit(@RequestBody BuMsgPduOut buMsgPduOut)
//    {
//        return toAjax(buMsgPduOutService.updateBuMsgPduOut(buMsgPduOut));
//    }
//
//    /**
//     * 删除pdu-out消息
//     */
//    @PreAuthorize("@ss.hasPermi('devicemsg:out:remove')")
//    @Log(title = "pdu-out消息", businessType = BusinessType.DELETE)
//	@DeleteMapping("/{ids}")
//    public AjaxResult remove(@PathVariable Long[] ids)
//    {
//        return toAjax(buMsgPduOutService.deleteBuMsgPduOutByIds(ids));
//    }
//}
