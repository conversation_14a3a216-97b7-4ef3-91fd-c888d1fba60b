package com.snct.common.transfer;

/**
 * <br>
 * <b>功能描述:</b>
 *
 * <AUTHOR>
 */
public class TransferPackage {

    /**
     * 船只的序列号
     */
    private String sn;

    /**
     * 统一编码
     */
    private Integer commandNum;


    /**
     * 记录编号
     */
    private Long recordNum;

    /**
     * 记录时间
     */
    private Long time;

    /**
     * 包类型  0：传感器设备数据 1：图片数据  2：UDP信号维持 3：数据同步
     */
    private Integer packageType;

    /**
     * 传感器设备类型
     */
    private Integer deviceType;

    /**
     * 设备编号
     */
    private String deviceCode;

    /**
     * 拆包个数(图片数据)
     */
    private Integer unpackingTotal;

    /**
     * 拆包编号(图片数据)
     */
    private Integer unpackingNum;

    /**
     * 是否补发操作
     */
    private Integer isRepair;

    /**
     * 包数据
     */
    private String message;

    public TransferPackage(){}

    public TransferPackage(Long num, Long time, Integer packageType, Integer isRepair, String message) {
        this.commandNum = Integer.valueOf(num.toString());
        this.time = time;
        this.packageType = packageType;
        this.isRepair = isRepair;
        this.message = message;
    }

    /**
     * 传感器设备构造
     * @param num
     * @param time
     * @param packageType
     * @param isRepair
     * @param message
     * @param deviceCode
     */
    public TransferPackage(Long num, Long time, Integer packageType, Integer isRepair, String message, String deviceCode){
        this(num,time,packageType,isRepair,message);

        this.deviceCode = deviceCode;
    }

    /**
     * 传感器设备构造
     * @param num
     * @param time
     * @param packageType
     * @param isRepair
     * @param message
     * @param deviceType
     * @param deviceCode
     */
    public TransferPackage(Long num, Long time, Integer packageType, Integer isRepair, String message, Integer deviceType, String deviceCode){
        this(num,time,packageType,isRepair,message);

        this.deviceType = deviceType;
        this.deviceCode = deviceCode;
    }

    /**
     * 图片数据构造
     * @param num
     * @param time
     * @param packageType
     * @param isRepair
     * @param message
     * @param unpackingTotal
     * @param unpackingNum
     */
    public TransferPackage(Long num, Long time, Integer packageType, Integer isRepair, String message, Integer unpackingTotal, Integer unpackingNum){
        this(num,time,packageType,isRepair,message);

        this.unpackingTotal = unpackingTotal;
        this.unpackingNum = unpackingNum;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public Integer getCommandNum() {
        return commandNum;
    }

    public void setCommandNum(Integer commandNum) {
        this.commandNum = commandNum;
    }

    public Long getTime() {
        return time;
    }

    public void setTime(Long time) {
        this.time = time;
    }

    public Integer getPackageType() {
        return packageType;
    }

    public void setPackageType(Integer packageType) {
        this.packageType = packageType;
    }

    public Integer getIsRepair() {
        return isRepair == null ? 0 : isRepair;
    }

    public void setIsRepair(Integer isRepair) {
        this.isRepair = isRepair;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Integer getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(Integer deviceType) {
        this.deviceType = deviceType;
    }

    public String getDeviceCode() {
        return deviceCode;
    }

    public void setDeviceCode(String deviceCode) {
        this.deviceCode = deviceCode;
    }

    public Integer getUnpackingTotal() {
        return unpackingTotal;
    }

    public void setUnpackingTotal(Integer unpackingTotal) {
        this.unpackingTotal = unpackingTotal;
    }

    public Integer getUnpackingNum() {
        return unpackingNum;
    }

    public void setUnpackingNum(Integer unpackingNum) {
        this.unpackingNum = unpackingNum;
    }
}