package com.snct.hbase.domain.engineroom;

import com.snct.hbase.utils.AnalysisUtils;

import java.util.Map;

/**
 * 应急发动机
 * <AUTHOR>
 */
public class EmergencyGenerator {

    /**
     * 应急发动机转速  04001
     */
    private String speed;

    /**
     * 应急发动机功率  19107.15
     */
    private String power;

    /**
     * 应急发动机功率因数  19107.16
     */
    private String powerFactor;

    /**
     * 应急发动机L1-L2相电压  19107.8
     */
    private String voltageL1L2;

    /**
     * 应急发动机L2-L3相电压  19107.9
     */
    private String voltageL2L3;

    /**
     * 应急发动机L3-L1相电压  19107.10
     */
    private String voltageL3L1;

    /**
     * 应急发动机L1相电流  19107.11
     */
    private String currentL1;

    /**
     * 应急发动机L2相电流  19107.12
     */
    private String currentL2;

    /**
     * 应急发动机L3相电流  19107.13
     */
    private String currentL3;

    /**
     * 应急发动机L1相频率  19107.14.1
     */
    private String frequencyL1;

    /**
     * 应急发动机L2相频率  19107.14.2
     */
    private String frequencyL2;

    /**
     * 应急发动机L3相频率  19107.14.3
     */
    private String frequencyL3;

    /**
     * 应急发动机定子绕组U温度  04020
     */
    private String tempU;

    /**
     * 应急发动机定子绕组U温度  04021
     */
    private String tempV;

    /**
     * 应急发动机定子绕组U温度  04022
     */
    private String tempW;

    /**
     * 数据时间
     */
    private Long timeStamp;

    public String getSpeed() {
        return speed;
    }

    public void setSpeed(String speed) {
        this.speed = speed;
    }

    public String getPower() {
        return power;
    }

    public void setPower(String power) {
        this.power = power;
    }

    public String getPowerFactor() {
        return powerFactor;
    }

    public void setPowerFactor(String powerFactor) {
        this.powerFactor = powerFactor;
    }

    public String getVoltageL1L2() {
        return voltageL1L2;
    }

    public void setVoltageL1L2(String voltageL1L2) {
        this.voltageL1L2 = voltageL1L2;
    }

    public String getVoltageL2L3() {
        return voltageL2L3;
    }

    public void setVoltageL2L3(String voltageL2L3) {
        this.voltageL2L3 = voltageL2L3;
    }

    public String getVoltageL3L1() {
        return voltageL3L1;
    }

    public void setVoltageL3L1(String voltageL3L1) {
        this.voltageL3L1 = voltageL3L1;
    }

    public String getCurrentL1() {
        return currentL1;
    }

    public void setCurrentL1(String currentL1) {
        this.currentL1 = currentL1;
    }

    public String getCurrentL2() {
        return currentL2;
    }

    public void setCurrentL2(String currentL2) {
        this.currentL2 = currentL2;
    }

    public String getCurrentL3() {
        return currentL3;
    }

    public void setCurrentL3(String currentL3) {
        this.currentL3 = currentL3;
    }

    public String getFrequencyL1() {
        return frequencyL1;
    }

    public void setFrequencyL1(String frequencyL1) {
        this.frequencyL1 = frequencyL1;
    }

    public String getFrequencyL2() {
        return frequencyL2;
    }

    public void setFrequencyL2(String frequencyL2) {
        this.frequencyL2 = frequencyL2;
    }

    public String getFrequencyL3() {
        return frequencyL3;
    }

    public void setFrequencyL3(String frequencyL3) {
        this.frequencyL3 = frequencyL3;
    }

    public String getTempU() {
        return tempU;
    }

    public void setTempU(String tempU) {
        this.tempU = tempU;
    }

    public String getTempV() {
        return tempV;
    }

    public void setTempV(String tempV) {
        this.tempV = tempV;
    }

    public String getTempW() {
        return tempW;
    }

    public void setTempW(String tempW) {
        this.tempW = tempW;
    }

    public Long getTimeStamp() {
        return timeStamp;
    }

    public void setTimeStamp(Long timeStamp) {
        this.timeStamp = timeStamp;
    }

    public EmergencyGenerator(){

    }

    public EmergencyGenerator(Map<String,EngineroomData> map){

        this.speed = AnalysisUtils.analysis(map.get("04001"));
        this.power = AnalysisUtils.analysis(map.get("19107.15"));
        this.powerFactor = AnalysisUtils.analysis(map.get("19107.16"));
        this.voltageL1L2 = AnalysisUtils.analysis(map.get("19107.8"));
        this.voltageL2L3 = AnalysisUtils.analysis(map.get("19107.9"));
        this.voltageL3L1 = AnalysisUtils.analysis(map.get("19107.10"));
        this.currentL1 = AnalysisUtils.analysis(map.get("19107.11"));
        this.currentL2 = AnalysisUtils.analysis(map.get("19107.12"));
        this.currentL3 = AnalysisUtils.analysis(map.get("19107.13"));
        this.frequencyL1 = AnalysisUtils.analysis(map.get("19107.14.1"));
        this.frequencyL2 = AnalysisUtils.analysis(map.get("19107.14.2"));
        this.frequencyL3 = AnalysisUtils.analysis(map.get("19107.14.3"));
        this.tempU = AnalysisUtils.analysis(map.get("04020"));
        this.tempV = AnalysisUtils.analysis(map.get("04021"));
        this.tempW = AnalysisUtils.analysis(map.get("04022"));

    }

    public EmergencyGenerator(String msg){

        String[] strbuff = msg.split(",",16);
        this.speed = strbuff[0];
        this.power =strbuff[1];
        this.powerFactor =strbuff[2];
        this.voltageL1L2 =strbuff[3];
        this.voltageL2L3 = strbuff[4];
        this.voltageL3L1 =strbuff[5];
        this.currentL1 =strbuff[6];
        this.currentL2 =strbuff[7];
        this.currentL3 = strbuff[8];
        this.frequencyL1 =strbuff[9];
        this.frequencyL2 =strbuff[10];
        this.frequencyL3 =strbuff[11];
        this.tempU = strbuff[12];
        this.tempV =strbuff[13];
        this.tempW =strbuff[14];
        this.timeStamp = Long.parseLong(strbuff[15]);
    }

    @Override
    public String toString() {
        return "EmergencyGenerator{" +
                "speed='" + speed + '\'' +
                ", power='" + power + '\'' +
                ", powerFactor='" + powerFactor + '\'' +
                ", voltageL1L2='" + voltageL1L2 + '\'' +
                ", voltageL2L3='" + voltageL2L3 + '\'' +
                ", voltageL3L1='" + voltageL3L1 + '\'' +
                ", currentL1='" + currentL1 + '\'' +
                ", currentL2='" + currentL2 + '\'' +
                ", currentL3='" + currentL3 + '\'' +
                ", frequencyL1='" + frequencyL1 + '\'' +
                ", frequencyL2='" + frequencyL2 + '\'' +
                ", frequencyL3='" + frequencyL3 + '\'' +
                ", tempU='" + tempU + '\'' +
                ", tempV='" + tempV + '\'' +
                ", tempW='" + tempW + '\'' +
                ", timeStamp=" + timeStamp +
                '}';
    }

    public String mergeSendStr() {
        StringBuffer sb = new StringBuffer();
        sb.append(speed == null ? "" : speed).append(",");
        sb.append(power == null ? "" : power).append(",");
        sb.append(powerFactor == null ? "" : powerFactor).append(",");
        sb.append(voltageL1L2 == null ? "" : voltageL1L2).append(",");
        sb.append(voltageL2L3 == null ? "" : voltageL2L3).append(",");
        sb.append(voltageL3L1 == null ? "" : voltageL3L1).append(",");
        sb.append(currentL1 == null ? "" : currentL1).append(",");
        sb.append(currentL2 == null ? "" : currentL2).append(",");
        sb.append(currentL3 == null ? "" : currentL3).append(",");
        sb.append(frequencyL1 == null ? "" : frequencyL1).append(",");
        sb.append(frequencyL2 == null ? "" : frequencyL2).append(",");
        sb.append(frequencyL3 == null ? "" : frequencyL3).append(",");
        sb.append(tempU == null ? "" : tempU).append(",");
        sb.append(tempV == null ? "" : tempV).append(",");
        sb.append(tempW == null ? "" : tempW).append(",");
        sb.append(timeStamp == null ? "" : timeStamp);
        return sb.toString();
    }
}
