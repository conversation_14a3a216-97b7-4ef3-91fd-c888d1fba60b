package com.snct.handler;

import com.alibaba.fastjson.JSONObject;
import com.snct.common.utils.NumUtils;
import com.snct.common.utils.spring.SpringUtils;
import com.snct.service.RepairService;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import io.netty.channel.socket.DatagramPacket;
import io.netty.util.ReferenceCountUtil;
import java.nio.charset.StandardCharsets;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class NormalUdpClientHandler extends ChannelInboundHandlerAdapter {
    protected Logger logger = LoggerFactory.getLogger(NormalUdpClientHandler.class);

    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        super.channelActive(ctx);
        try {
            String uuid = String.valueOf(ctx.channel().id());
            this.logger.info("UDP广播开始：{}", uuid);
        } catch (Exception e) {
            this.logger.error("关闭UDP广播，", e);
            ctx.channel().close();
        }
    }

    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        this.logger.info("UDP广播结束：{}", ctx.channel().id());
        super.channelInactive(ctx);
    }

    public void channelRead(ChannelHandlerContext ctx, Object msg) {
        try {
            DatagramPacket packet = (DatagramPacket) msg;
            ByteBuf byteBuf = packet.copy().content();
            byte[] allBytes = new byte[byteBuf.readableBytes()];
            byteBuf.readBytes(allBytes);
            byte[] typeByte = new byte[1];
            System.arraycopy(allBytes, 0, typeByte, 0, 1);
            String type = new String(typeByte);
            byte[] contentByte = new byte[allBytes.length - 1];
            System.arraycopy(allBytes, 1, contentByte, 0, allBytes.length - 1);
            this.logger.info("UDP接收到补数据00111，--type-{}", type);
            RepairService repairService = SpringUtils.getBean(RepairService.class);
            if ("0".equals(type)) {
                if (contentByte.length % 4 != 0) {
                    return;
                }
                List<Integer> commandNumList = NumUtils.byteArray2List(contentByte);
                this.logger.info("UDP接收到补数据请求111，--{}", JSONObject.toJSONString(commandNumList));
                repairService.repairData(commandNumList);
            } else if ("1".equals(type)) {
                String repairMessage = new String(contentByte, StandardCharsets.UTF_8);
                this.logger.info("UDP补包111,----{}", repairMessage);
                repairService.repairPackage(repairMessage);
            }
            ReferenceCountUtil.release(msg);
        } finally {
            ReferenceCountUtil.release(msg);
        }
    }

    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        String uuid = String.valueOf(ctx.channel().id());
        this.logger.error("ChannelInboundHandler_Exception：UDP广播异常：{}，ip：{}，error：{}", new Object[]{uuid, "", cause});
    }
}