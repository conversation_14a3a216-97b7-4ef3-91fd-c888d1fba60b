//package com.snct.mqtt;
//
//
//import org.eclipse.paho.client.mqttv3.*;
//import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;
//
//public class MqttListener {
//
//    public static void main(String[] args) {
//        // MQTT配置参数
//        String broker = "tcp://mqtt.bds100.com:31565"; // 修改为你的MQTT服务器地址和端口
//        String clientId = "snct01"; // MQTT客户端ID，保持唯一性
//        String topic = "GET/SZSN/bgv2t"; // 要订阅的主题
//        int qos = 1; // 服务质量等级 (0-最多一次, 1-至少一次, 2-恰好一次)
//
//        try {
//            // 1. 创建MQTT客户端
//            MqttClient client = new MqttClient(
//                    broker,
//                    clientId,
//                    new MemoryPersistence()
//            );
//
//            // 2. 配置连接选项
//            MqttConnectOptions options = new MqttConnectOptions();
//            options.setCleanSession(true);
//            options.setAutomaticReconnect(true); // 自动重连
//            options.setUserName("odsc-SZSN");
//            options.setPassword("hKrJVztPjDdS".toCharArray());
//            options.setConnectionTimeout(10);    // 连接超时(秒)
//
//            // 3. 设置回调处理器
//            client.setCallback(new MqttCallback() {
//                @Override
//                public void connectionLost(Throwable cause) {
//                    System.out.println("连接断开! 尝试重连中...");
//                }
//
//                @Override
//                public void messageArrived(String topic, MqttMessage message) {
//                    String payload = new String(message.getPayload());
//                    System.out.println("\n收到消息: ");
//                    System.out.println("主题: " + topic);
//                    System.out.println("内容: " + payload);
//                    System.out.println("QoS: " + message.getQos());
//                }
//
//                @Override
//                public void deliveryComplete(IMqttDeliveryToken token) {
//                    // 消息发布完成回调 (订阅者不需要实现)
//                }
//            });
//
//            // 4. 连接到服务器
//            System.out.println("连接到MQTT服务器: " + broker);
//            client.connect(options);
//            System.out.println("连接成功!");
//
//            // 5. 订阅主题
//            client.subscribe(topic, qos);
//            System.out.println("订阅主题: " + topic + " [QoS " + qos + "]");
//            System.out.println("等待消息... (按Ctrl+C退出)");
//
//            // 保持程序运行
//            while (true) {
//                Thread.sleep(1000);
//            }
//
//        } catch (MqttException | InterruptedException e) {
//            e.printStackTrace();
//        }
//    }
//}