package com.snct.hbase.domain.engineroom.vo;

import com.snct.hbase.annotation.Excel;
import com.snct.hbase.annotation.HBaseColumn;
import com.snct.hbase.annotation.HBaseTable;
import com.snct.hbase.utils.RowKeyUtils;

/**
 * 站位距离数据
 * 每分钟记录船的GPS、船速、马达转速、和已规划的站位的距离
 * hbase表名 ns1:s_station_distance_aa0001, 其中s_: secondary二次组合表; _aa0001：sn号
 * <AUTHOR>
 */
@HBaseTable(tableName = "ns1:s_station_distance_aa0001")
public class StationDistanceVo {

    @HBaseColumn(family = "rowkey", qualifier = "rowkey")
    private String id;

    private Long time;

    @Excel(name = "时间")
    private String bjTime;

    /**
     * 纬度
     */
    @Excel(name = "纬度")
    @HBaseColumn(family = "i", qualifier = "lat")
    private String latitude;

    /**
     * 经度
     */
    @Excel(name = "经度")
    @HBaseColumn(family = "i", qualifier = "long")
    private String longitude;

    /**
     * 地面速率
     */
    @Excel(name = "船速")
    @HBaseColumn(family = "i", qualifier = "gr")
    private String groundRate;

    /**
     * 主推4马达转速
     */
    @Excel(name = "主推4马达转速")
    @HBaseColumn(family = "i", qualifier = "m4ms")
    private String mt4MtrSpeed;

    /**
     * 主推5马达转速
     */
    @Excel(name = "主推5马达转速")
    @HBaseColumn(family = "i", qualifier = "m5ms")
    private String mt5MtrSpeed;

    /**
     * 和站位的距离
     */
    @Excel(name = "和站位的距离")
    @HBaseColumn(family = "i", qualifier = "dis")
    private String distanceMap;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Long getTime() {
        return RowKeyUtils.getTimeByRowKey(this.id);
    }

    public void setTime(Long time) {
        this.time = time;
    }

    public String getBjTime() {
        return RowKeyUtils.getBjTimeByRowKey(this.id);
    }

    public void setBjTime(String bjTime) {
        this.bjTime = bjTime;
    }

    public String getMt4MtrSpeed() {
        return mt4MtrSpeed;
    }

    public void setMt4MtrSpeed(String mt4MtrSpeed) {
        this.mt4MtrSpeed = mt4MtrSpeed;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getGroundRate() {
        return groundRate;
    }

    public void setGroundRate(String groundRate) {
        this.groundRate = groundRate;
    }

    public String getMt5MtrSpeed() {
        return mt5MtrSpeed;
    }

    public void setMt5MtrSpeed(String mt5MtrSpeed) {
        this.mt5MtrSpeed = mt5MtrSpeed;
    }

    public String getDistanceMap() {
        return distanceMap;
    }

    public void setDistanceMap(String distanceMap) {
        this.distanceMap = distanceMap;
    }
}
