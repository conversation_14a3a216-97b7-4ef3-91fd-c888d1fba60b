package com.snct.network.controller;

import com.snct.common.annotation.Log;
import com.snct.common.core.domain.AjaxResult;
import com.snct.common.enums.BusinessType;

import com.snct.network.domain.NetworkConfig;
import com.snct.network.service.NetworkService;
import com.snct.utils.IpUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import java.io.IOException;

@RequestMapping({"/network"})
@RestController
public class NetworkController {

    @Autowired
    private NetworkService networkService;

    @GetMapping({"/getNetConfig"})
    @PreAuthorize("@ss.hasPermi('system:network:list')")
    public AjaxResult getNetConfig() throws IOException {
        return AjaxResult.success(networkService.getNetConfigByNmcli());
    }

    @Log(title = "网卡配置", businessType = BusinessType.UPDATE)
    @PutMapping({"/update"})
    @PreAuthorize("@ss.hasPermi('system:network:edit')")
    public AjaxResult updateNetwork(@Validated @RequestBody NetworkConfig networkConfig) throws Exception {
        if ("STATIC".equals(networkConfig.getMode()) && StringUtils.isEmpty(networkConfig.getIp())) {
            throw new Exception("ip不能为空");
        }
        if ("STATIC".equals(networkConfig.getMode()) && StringUtils.isEmpty(networkConfig.getNetmask())) {
            throw new Exception("子网掩码不能为空");
        }
        if (StringUtils.isNotEmpty(networkConfig.getIp()) && !IpUtil.ipCheck(networkConfig.getIp())) {
            throw new Exception("ip格式不正确");
        }
        if (StringUtils.isNotEmpty(networkConfig.getNetmask()) && !IpUtil.ipCheck(networkConfig.getNetmask())) {
            throw new Exception("掩码格式不正确");
        }
        if (StringUtils.isNotEmpty(networkConfig.getGateway()) && !IpUtil.ipCheck(networkConfig.getGateway())) {
            throw new Exception("网关格式不正确");
        }
        if (StringUtils.isNotEmpty(networkConfig.getDns1()) && !IpUtil.ipCheck(networkConfig.getDns1())) {
            throw new Exception("dns1 ip格式不正确");
        }
        if (StringUtils.isNotEmpty(networkConfig.getDns2()) && !IpUtil.ipCheck(networkConfig.getDns2())) {
            throw new Exception("dns2 ip格式不正确");
        }
        if (StringUtils.isNotEmpty(networkConfig.getVisit1()) && !IpUtil.isIpMask(networkConfig.getVisit1())) {
            throw new Exception("访问网段1格式不正确");
        }
        if (StringUtils.isNotEmpty(networkConfig.getVisit2()) && !IpUtil.isIpMask(networkConfig.getVisit2())) {
            throw new Exception("访问网段2格式不正确");
        }
        if (StringUtils.isNotEmpty(networkConfig.getVisit3()) && !IpUtil.isIpMask(networkConfig.getVisit3())) {
            throw new Exception("访问网段3格式不正确");
        }

        // 检查IP地址是否与现有网卡配置存在同一网段冲突
        networkService.checkSameSubnetConflict(networkConfig);

        return AjaxResult.success(networkService.updateNetwork(networkConfig));
    }
}