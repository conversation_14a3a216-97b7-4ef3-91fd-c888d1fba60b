package com.snct.kafka.handler;

import com.alibaba.fastjson.JSONObject;
import com.snct.kafka.KafkaMessage;
import com.snct.system.domain.msg.BuMsgModem;
import com.snct.system.service.IBuMsgModemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 猫设备数据处理器
 *
 * <AUTHOR>
 */
@Component
public class ModemDeviceHandler extends AbstractDeviceHandler {

    @Autowired
    private IBuMsgModemService buMsgModemService;

    @Override
    protected boolean saveToMysql(JSONObject jsonObject, String sn, Map<String, Object> deviceInfo) {
        try {
            // 创建MySQL实体对象
            BuMsgModem modem = new BuMsgModem();
            
            // 设置基本字段
            Long deviceId = jsonObject.getLong("deviceId");
            modem.setDeptId((Long)deviceInfo.get("deptId"));
            modem.setShipId((Long)deviceInfo.get("shipId"));
            modem.setDeviceId(deviceId);
            
            // 设置设备特定字段
            modem.setSignal(jsonObject.getDouble("signal"));
            modem.setSpeed(jsonObject.getDouble("speed"));
            modem.setSendPower(jsonObject.getDouble("sendPower"));
            modem.setIsFlag(jsonObject.getLong("isFlag"));
            modem.setStatus(0L); // 默认状态

            // 保存到MySQL
            buMsgModemService.insertBuMsgModem(modem);
            return true;
        } catch (Exception e) {
            logger.error("保存猫数据到MySQL失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    protected boolean saveToHbase(KafkaMessage kafkaMessage) {
        try {
            // 保存到HBase
            storeService.save2Hbase(kafkaMessage);
            return true;
        } catch (Exception e) {
            logger.error("保存猫数据到HBase失败: {}", e.getMessage(), e);
            return false;
        }
    }
} 