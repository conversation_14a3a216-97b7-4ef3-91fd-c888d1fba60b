package com.snct.hbase.utils;

import com.snct.hbase.annotation.HBaseColumn;
import com.snct.hbase.domain.hbase.HbaseColumnVo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.NavigableMap;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * @Author: tsohan
 * @Descriptions:
 */
public class HBaseBeanUtil {

    private static final Logger logger = LoggerFactory.getLogger(HBaseBeanUtil.class);



    /**
     * 获取rowkey
     *
     * @param columnMap
     * @return
     */
    private static String getObjId(Map<String, HbaseColumnVo> columnMap) {
        for (Map.Entry<String, HbaseColumnVo> cm : columnMap.entrySet()) {
            if (cm.getValue() == null) {
                continue;
            }
            if ("rowkey".equalsIgnoreCase(cm.getValue().getQualifier()) && "rowkey".equalsIgnoreCase(cm.getValue().getFamily())) {
                return cm.getValue().getValue();
            }
        }
        return "";
    }

    /**
     * 获取Bean中的id,作为Rowkey
     *
     * @param <T>
     * @param obj
     * @return
     */
    public static <T> String parseObjId(T obj) {
        Class<?> clazz = obj.getClass();
        try {
            Field field = clazz.getDeclaredField("id");
            field.setAccessible(true);
            Object object = field.get(obj);
            return object.toString();
        } catch (NoSuchFieldException e) {
            logger.error("", e);
        } catch (SecurityException e) {
            logger.error("", e);
        } catch (IllegalArgumentException e) {
            logger.error("", e);
        } catch (IllegalAccessException e) {
            logger.error("", e);
        }
        return "";
    }





    /**
     * 获取对象的set方法
     *
     * @param obj
     * @param <T>
     * @return
     * @throws NoSuchMethodException
     */
    private static <T> Map<String, HbaseColumnVo> getObjColumnMap(T obj) throws NoSuchMethodException, IllegalAccessException {
        Map<String, HbaseColumnVo> columnInfoMap = new HashMap<>();
        Class<?> clazz = obj.getClass();
        Field[] fields = clazz.getDeclaredFields();
        HbaseColumnVo hbaseColumnVo;

        for (Field field : fields) {
            if (!field.isAnnotationPresent(HBaseColumn.class)) {
                continue;
            }
            hbaseColumnVo = new HbaseColumnVo();

            field.setAccessible(true);
            HBaseColumn orm = field.getAnnotation(HBaseColumn.class);
            String family = orm.family();
            String qualifier = orm.qualifier();
            if (StringUtils.isBlank(family) || StringUtils.isBlank(qualifier)) {
                continue;
            }
            hbaseColumnVo.setFamily(family);
            hbaseColumnVo.setQualifier(qualifier);

            String fieldName = field.getName();
            String firstLetter = fieldName.substring(0, 1).toUpperCase();
            String setMethodName = "set" + firstLetter + fieldName.substring(1);
            Method setMethod = clazz.getMethod(setMethodName, new Class[]{field.getType()});
            hbaseColumnVo.setSetMethod(setMethod);

            if (field.get(obj) != null && StringUtils.isNotBlank(field.get(obj).toString())) {
                hbaseColumnVo.setValue(field.get(obj).toString());
            }

            columnInfoMap.put(qualifier, hbaseColumnVo);
        }

        return columnInfoMap;
    }

    /**
     * 获取Result中的对应的值
     *
     * @param result
     * @return
     */
    private static Map<String, String> getResultValueMap(Object result) {

        Map<String, String> valueMap = new HashMap<>();

        return valueMap;
    }


}
