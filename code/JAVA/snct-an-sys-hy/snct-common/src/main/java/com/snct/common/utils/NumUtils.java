package com.snct.common.utils;

import org.apache.commons.compress.utils.Lists;

import java.nio.ByteBuffer;
import java.util.List;

public class NumUtils {
    private static ByteBuffer buffer = ByteBuffer.allocate(8);

    public static byte[] int2ByteArray(int i) {
        byte[] result = new byte[4];
        result[0] = (byte) (i >> 24 & 0xFF);
        result[1] = (byte) (i >> 16 & 0xFF);
        result[2] = (byte) (i >> 8 & 0xFF);
        result[3] = (byte) (i & 0xFF);
        return result;
    }

    public static byte[] list2ByteArray(List<Integer> list) {
        byte[] result = new byte[list.size() * 4];
        int offset = 0;
        for (int i = 0; i < list.size(); i++) {
            byte[] bytes = int2ByteArray(list.get(i));
            mergeBytes(result, offset, bytes);
            offset += 4;
        }
        return result;
    }

    public static int byteArray2Int(byte[] bytes) {
        int value = 0;
        for (int i = 0; i < 4; i++) {
            int shift = (3 - i) * 8;
            value += (bytes[i] & 0xFF) << shift;
        }
        return value;
    }

    public static List<Integer> byteArray2List(byte[] bytes) {
        List<Integer> list = Lists.newArrayList();
        int count = bytes.length / 4;
        for (int i = 0; i < count; i++) {
            byte[] b = new byte[4];
            System.arraycopy(bytes, 4 * i, b, 0, 4);
            list.add(byteArray2Int(b));
        }
        return list;
    }

    public static byte[] longToBytes(long x) {
        buffer.putLong(0, x);
        return buffer.array();
    }

    public static long bytesToLong(byte[] bs) throws Exception {
        int bytes = bs.length;
        if (bytes > 1 && (
                bytes % 2 != 0 || bytes > 8))
            throw new Exception("not support");
        switch (bytes) {
            case 0:
                return 0L;
            case 1:
                return (bs[0] & 0xFF);
            case 2:
                return ((bs[0] & 0xFF) << 8 | bs[1] & 0xFF);
            case 4:
                return (bs[0] & 0xFFL) << 24L | (bs[1] & 0xFFL) << 16L | (bs[2] & 0xFFL) << 8L | bs[3] & 0xFFL;
            case 8:
                return (bs[0] & 0xFFL) << 56L | (bs[1] & 0xFFL) << 48L | (bs[2] & 0xFFL) << 40L | (bs[3] & 0xFFL) << 32L | (bs[4] & 0xFFL) << 24L | (bs[5] & 0xFFL) << 16L | (bs[6] & 0xFFL) << 8L | bs[7] & 0xFFL;
        }
        throw new Exception("not support");
    }

    public static int mergeBytes(byte[] total, int offset, byte[] target) {
        for (int i = 0; i < target.length; i++)
            total[offset + i] = target[i];
        return offset + target.length;
    }

    public static int mergeByte(byte[] total, int offset, byte target) {
        total[offset] = target;
        return offset + 1;
    }

    public static byte[] splitBytes(byte[] total, int offset, int len) {
        byte[] data = new byte[len];
        for (int i = 0; i < len; i++)
            data[i] = total[offset + i];
        return data;
    }

    public static byte splitByte(byte[] total, int offset) {
        return total[offset];
    }
}
